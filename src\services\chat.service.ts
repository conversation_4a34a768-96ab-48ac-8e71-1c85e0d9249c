import { SSEResponse, Task } from '@/types/project'
import { SSEService } from './sse.service'

export class ChatService {
    private sseService = new SSEService()
    private baseUrl = process.env.NEXT_PUBLIC_API_URL

    async sendMessage(message: string, sessionId: string, onContentUpdate: (content: string) => void): Promise<void> {
        const response = await fetch(`${this.baseUrl}/chat/planner`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Accept: 'text/event-stream',
            },
            body: JSON.stringify({
                message,
                sessionId,
                generateTasks: false,
            }),
        })

        if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`)
        }

        let fullContent = ''

        await this.sseService.processStream<SSEResponse>(response, (data) => {
            if (data.content) {
                fullContent += data.content
                onContentUpdate(fullContent)
            }
        })
    }

    async generateTasks(
        sessionId: string,
        onTaskGenerated: (task: Task) => void,
        onComplete: (count: number) => void,
    ): Promise<void> {
        const response = await fetch(`${this.baseUrl}/chat/planner`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Accept: 'text/event-stream',
            },
            body: JSON.stringify({
                message: 'generate_tasks',
                sessionId,
                generateTasks: true,
            }),
        })

        if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`)
        }

        const generatedTasks: Task[] = []

        await this.sseService.processStream<SSEResponse>(response, (data) => {
            if (data.message === 'Stream completed') {
                onComplete(generatedTasks.length)
                return
            }

            if (data.task && this.isValidTask(data.task)) {
                generatedTasks.push(data.task)
                onTaskGenerated(data.task)
            }
        })
    }

    private isValidTask(task: Task): boolean {
        return !!(task.id && task.title && task.description)
    }
}
