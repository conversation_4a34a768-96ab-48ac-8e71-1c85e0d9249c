'use client'

import { useState, useEffect, useMemo } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

export function Navbar() {
    const [scrolled, setScrolled] = useState(false)
    const pathname = usePathname()

    const showNavbarOnPages = useMemo(() => ['/', '/about', '/docs'], [])
    // Hide navbar on dashboard route

    // Always declare hooks at the top level
    useEffect(() => {
        // Don't add event listeners on dashboard route

        if (!showNavbarOnPages.includes(pathname)) return

        const handleScroll = () => {
            setScrolled(window.scrollY > 10)
        }

        window.addEventListener('scroll', handleScroll)
        return () => window.removeEventListener('scroll', handleScroll)
    }, [pathname, showNavbarOnPages])

    if (!showNavbarOnPages.includes(pathname)) {
        return null
    }

    // We now show the navbar on all pages, including auth pages

    return (
        <div className="fixed top-0 left-0 right-0 z-[100] flex justify-center p-4">
            <nav
                className={cn(
                    'flex items-center justify-between w-full max-w-7xl px-6 py-3 rounded-full transition-all duration-300',
                    scrolled ? 'bg-white/85 backdrop-blur-md shadow-md border border-gray-100' : 'bg-white/70 backdrop-blur-sm',
                )}>
                {/* Logo with Home Link */}
                <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
                    <Image src="/assets/img/raydian-logo.png" alt="Logo" width={28} height={28} className="h-auto w-auto" />
                    <span className="font-semibold text-lg">Radian</span>
                </Link>

                {/* Navigation Links */}
                <div className="hidden md:flex items-center gap-8">
                    <Link href="/about" className="text-sm font-medium text-gray-600 hover:text-primary transition-colors">
                        About
                    </Link>
                    <Link href="/docs" className="text-sm font-medium text-gray-600 hover:text-primary transition-colors">
                        Documentation
                    </Link>
                </div>

                {/* Login Button */}
                <Button size="sm" variant={'outline'} className="shadow-sm hover:shadow-md transition-shadow">
                    <Link href="/sign-in">Login</Link>
                </Button>
            </nav>
        </div>
    )
}
