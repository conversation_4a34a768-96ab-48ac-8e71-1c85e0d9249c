'use client'

import { ColumnDef, flexRender, getCoreRowModel, useReactTable, RowSelectionState } from '@tanstack/react-table'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from './ui/button'
import { useState } from 'react'

interface DataTableProps<TData extends { id: string }, TValue> {
    columns: ColumnDef<TData, TValue>[]
    data: TData[]
    headerCellClassName?: string
    tableContainerClassName?: string
    handlePreviousPage?: () => void
    handleNextPage?: () => void
    paginationDetails?: {
        currentPage: number
        totalPages: number
        total: number
        pageSize: number
    }
    customPaginationActions?: React.ReactNode
    onRowClick?: (row: TData) => void
}

export function DataTable<TData extends { id: string }, TValue>({
    columns,
    data,
    headerCellClassName = 'bg-white text-[#71717A] text-sm w-fit',
    tableContainerClassName = 'rounded-sm border overflow-hidden shadow-sm shadow-gray-200',
    handlePreviousPage,
    handleNextPage,
    paginationDetails,
    customPaginationActions,
    onRowClick,
}: DataTableProps<TData, TValue>) {
    const [rowSelection, setRowSelection] = useState<RowSelectionState>({})
    const itemsOnCurrentPage = getItemsOnCurrentPage(
        paginationDetails?.total || 0,
        paginationDetails?.currentPage || 1,
        paginationDetails?.pageSize || 10,
    )

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        state: {
            rowSelection,
        },
        onRowSelectionChange: setRowSelection,
        enableRowSelection: true,
        getRowId: (row: TData) => row.id,
    })

    const selectedRowCount = Object.keys(rowSelection).length

    return (
        <>
            <div className={tableContainerClassName}>
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id} className={headerCellClassName}>
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(header.column.columnDef.header, header.getContext())}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody className="bg-white w-fit">
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && 'selected'}
                                    onClick={(e) => {
                                        const element = e.target as HTMLElement
                                        if (onRowClick && element.id !== 'select') {
                                            onRowClick(row.original)
                                        }
                                    }}
                                    className={onRowClick ? 'cursor-pointer hover:bg-gray-100 border-none' : 'border-none'}>
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id} className="border-none max-w-[230px] pr-7">
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="text-center">
                                    No results.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>

            {customPaginationActions && customPaginationActions}

            {!customPaginationActions && paginationDetails && (
                <div className="flex justify-between gap-4 my-2">
                    <div className="text-[#71717A] text-sm">
                        {selectedRowCount} of {itemsOnCurrentPage} row(s) selected
                    </div>
                    <div className="flex justify-center gap-4">
                        <Button
                            onClick={handlePreviousPage}
                            disabled={paginationDetails?.currentPage === 1}
                            className="bg-white text-[#09090B] hover:bg-gray-300 rounded-sm">
                            Previous
                        </Button>
                        <Button
                            onClick={handleNextPage}
                            disabled={paginationDetails?.currentPage === paginationDetails?.totalPages}
                            className="bg-white text-[#09090B] hover:bg-gray-300 rounded-sm">
                            Next
                        </Button>
                    </div>
                </div>
            )}
        </>
    )
}

function getItemsOnCurrentPage(total: number, currentPage: number, pageSize: number): number {
    if (currentPage < 1 || pageSize <= 0 || total < 0) return 0

    const fullPagesItemCount = (currentPage - 1) * pageSize
    const remainingItems = total - fullPagesItemCount

    return remainingItems >= pageSize ? pageSize : Math.max(remainingItems, 0)
}
