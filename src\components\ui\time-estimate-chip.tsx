'use client'

import { Timer } from 'lucide-react'
import type { FC } from 'react'

// Props for the TimeEstimateChip component
interface TimeEstimateChipProps {
    estimate: number // Estimated time in hours
    size?: 'sm' | 'md' | 'lg' // Size of the chip
}

/**
 * TimeEstimateChip component to display a styled chip for estimated time
 */
export const TimeEstimateChip: FC<TimeEstimateChipProps> = ({ estimate, size = 'md' }) => {
    // Size classes mapping
    const sizeClasses = {
        sm: 'text-xs px-1 py-[2px]',
        md: 'text-sm px-2 py-1',
        lg: 'text-base px-3 py-2',
    }

    return (
        <div
            className={`inline-flex items-center gap-1.5 rounded-md border bg-white border-violet-100 ${sizeClasses[size]}`}
            data-estimate={estimate}>
            <div className="flex justify-center items-center bg-[#6E65AA] rounded-full p-[3px]">
                <Timer size={14} className="text-white" />
            </div>
            {/* <div className="flex bg-violet-100 rounded-full w-[24px] h-[24px] justify-center items-center">
            </div> */}
            <span className="font-medium">{estimate} hrs</span>
        </div>
    )
}

export default TimeEstimateChip
