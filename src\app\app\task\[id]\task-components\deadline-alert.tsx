'use client'

import { useState } from 'react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface DeadlineAlertProps {
    title?: string
    description?: string
    className?: string
}

export default function DeadlineAlert({
    title = "You're Almost There! Deadline Tomorrow",
    description = "Great progress! You're in the final stretch. Remember, your task deadline is tomorrow. Review your tasks and let's finish strong!",
    className,
}: DeadlineAlertProps) {
    const [isVisible, setIsVisible] = useState(true)

    if (!isVisible) return null

    return (
        <Alert className={cn('pr-12 relative', className)}>
            <AlertTitle className="text-[#000000] font-medium text-[16px]">{title}</AlertTitle>
            <AlertDescription className="text-[#18181B] text-sm">{description}</AlertDescription>
            <Button
                variant="ghost"
                size="icon"
                className="absolute right-2 top-2 h-6 w-6 rounded-full p-0"
                onClick={() => setIsVisible(false)}>
                <X className="h-4 w-4" color="#717680" />
                <span className="sr-only">Close</span>
            </Button>
        </Alert>
    )
}
