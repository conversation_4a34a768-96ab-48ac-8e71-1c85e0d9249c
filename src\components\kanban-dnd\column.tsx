'use client'

import { useMemo, useState } from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { useDroppable } from '@dnd-kit/core'
import type { DnDColumnType as ColumnType, DnDTaskType as Task } from './types'
import TaskCard from './task-card'
import { Plus, ChevronDown } from 'lucide-react'
import { ScrollArea } from '../ui/scroll-area'
import { motion, AnimatePresence } from 'framer-motion'
import getStatusIcon from '../get-status-icon'
import { CreateTaskModal } from '@/app/app/planner/create-task'

interface ColumnProps {
    column: ColumnType
    tasks: Task[]
}

export default function Column({ column, tasks }: ColumnProps) {
    // Set up sortable for the column itself
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
        id: `column-${column.id}`,
        data: {
            type: 'column',
            column,
        },
    })

    // Set up droppable for the container area
    const { setNodeRef: setDroppableRef } = useDroppable({
        id: `container-${column.id}`,
        data: {
            type: 'container',
            column,
        },
    })

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
        // backgroundColor: column.colour + "20", // Add transparency
    }

    const taskIds = useMemo(() => tasks.map((task) => task.id), [tasks])

    const [isExpanded, setIsExpanded] = useState(true)

    return (
        <div
            ref={setNodeRef}
            style={style}
            {...attributes}
            {...listeners}
            className="flex flex-col w-[300px] bg-transparent flex-shrink-0">
            <div className="py-1 px-2 font-semibold rounded-md text-gray-700 cursor-move flex justify-between items-center bg-[#F4F4F4] mr-3">
                <div className="flex items-center justify-center gap-2">
                    {getStatusIcon(column.label)}
                    <span>{column.label}</span>
                    <span className="text-sm">({tasks.length})</span>
                </div>
                <div className="flex items-center gap-1">
                    <CreateTaskModal
                        modalTrigger={<Plus size={16} className="cursor-pointer" />}
                        defaultValues={{ statusName: column?.label, priorityName: 'medium' }}
                    />
                    <button
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="p-2 flex items-center justify-center text-sm text-gray-600 hover:bg-gray-100 rounded-md transition-colors">
                        <motion.div
                            animate={{ rotate: isExpanded ? 180 : 0 }}
                            transition={{ duration: 0.2 }}
                            className="cursor-pointer">
                            <ChevronDown size={16} />
                        </motion.div>
                    </button>
                </div>
            </div>

            <AnimatePresence initial={false}>
                {isExpanded && (
                    <motion.div
                        initial={{ height: 0, opacity: 0, y: -20 }}
                        animate={{
                            height: 'auto',
                            opacity: 1,
                            y: 0,
                            transition: {
                                height: { duration: 0.5 },
                                opacity: { duration: 0.5 },
                                y: { duration: 0.5, type: 'spring', stiffness: 300, damping: 25 },
                            },
                        }}
                        exit={{
                            height: 0,
                            opacity: 0,
                            y: -20,
                            transition: {
                                height: { duration: 0.5 },
                                opacity: { duration: 0.6 },
                                y: { duration: 0.5 },
                            },
                        }}
                        className="overflow-hidden">
                        <ScrollArea className="h-[70vh]  w-full py-2 pr-3">
                            <div
                                ref={setDroppableRef}
                                className="flex-1 flex flex-col gap-4 overflow-y-auto mt-2"
                                style={{ minHeight: '200px' }}>
                                {tasks.length === 0 ? (
                                    // Empty state that's explicitly droppable
                                    <div className="h-40 flex items-center justify-center text-gray-400 text-sm italic border-2 border-dashed border-gray-200 rounded-md">
                                        Drop tasks here
                                    </div>
                                ) : (
                                    <SortableContext items={taskIds} strategy={verticalListSortingStrategy}>
                                        {tasks.map((task) => (
                                            <TaskCard key={task.id} task={task} />
                                        ))}
                                    </SortableContext>
                                )}
                            </div>
                        </ScrollArea>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    )
}
