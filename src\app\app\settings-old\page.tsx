'use client'

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>s<PERSON>ist, <PERSON><PERSON>abs<PERSON>ontent, SPTabsTrigger } from '@/components/ui/scratchpad-tabs'

import Image from 'next/image'
import { useParams } from 'next/navigation'
import { useEffect } from 'react'
import ScratchPadView from './scratchpad-view'
import FeatureHistory from './feature-history'
import { FileText, History, Target } from 'lucide-react'
import ProjectDetailsSP from './project-details'
import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { useProjectStore } from '@/store/scratchpad-projects.store'

const ProjectPage = () => {
    const params = useParams<{ projectId: string }>() // Get project ID from URL
    const { selectedProject } = useProjectStore()
    const { setBreadcrumbs } = useBreadcrumbStore()

    const dashboardData = {
        projectName: 'Fun2Plan Dashboard',
        taskStatusDescription:
            'Overview of all tasks across all projects, with their respective statuses (e.g., To-Do, In Progress, Completed, Blocked).',
        techStack: ['Figma', 'Azure', 'Python'],
        teamMembers: [
            { id: 1, avatar: '/assets/avatars/member1.png' },
            { id: 2, avatar: '/assets/avatars/member2.png' },
            { id: 3, avatar: '/assets/avatars/member3.png' },
        ],
        totalTeamMembers: 25,
        featuresCompleted: 5,
        totalFeatures: 7,
        createdDate: '5/11/2024@ 4:57:28 AM',
        updatedDate: '7/11/2024@ 9:57:28 AM',
    }

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/' },
            { label: 'ScratchPad', href: '/app/scratchpad' },
            {
                label: selectedProject?.name || 'Project',
                href: `/app/scratchpad/${params.projectId}`,
            },
        ])
    }, [setBreadcrumbs, params.projectId, selectedProject?.name])

    return (
        <>
            <div className="mt-0 flex flex-col">
                <div className="flex flex-row gap-4 items-start">
                    <h1 className="leading-[34px] text-[25px] font-bold bg-gradient-to-r from-[#A29CD3] to-[#E8BFAC] text-transparent bg-clip-text">
                        Scratchpad
                    </h1>
                    <div className=" flex rounded-[24px] border border-[#2DA44E] text-[#2DA44E] text-[12px] px-[6px] font-medium">
                        Beta
                    </div>
                </div>
                <div className="flex flex-row items-center mt-[10px]">
                    <div className="flex justify-center items-center rounded-full bg-white w-[44px] h-[44px]">
                        <Image
                            src="/assets/icons/bot.svg"
                            className="h-auto w-auto"
                            width={21}
                            height={15}
                            alt="Scratchpad bot"
                        />
                    </div>
                    <div className="ml-[5px] flex flex-col">
                        <span className="text-[16px] font-bold">Introducing Kaizen Copilot Pad</span>
                        <span className="text-[12px] text-[#6E7681]">Your AI task generator is levelling up</span>
                    </div>
                </div>
                <SPTabs defaultValue="account" className="w-full mt-[20px]">
                    <SPTabsList className="grid w-full grid-cols-8">
                        <div className="flex flex-row">
                            <SPTabsTrigger className="cursor-pointer" value="account">
                                <Target className="mr-2" /> ScratchPad
                            </SPTabsTrigger>
                            <SPTabsTrigger className="cursor-pointer" value="password">
                                <History className="mr-2" /> History
                            </SPTabsTrigger>
                            <SPTabsTrigger className="cursor-pointer" value="project-details">
                                <FileText className="mr-2" /> Project Details
                            </SPTabsTrigger>
                        </div>
                    </SPTabsList>
                    <SPTabsContent className="relative" value="account">
                        <ScratchPadView />
                    </SPTabsContent>
                    <SPTabsContent className="relative" value="password">
                        <FeatureHistory />
                    </SPTabsContent>
                    <SPTabsContent value="project-details">
                        <ProjectDetailsSP {...dashboardData} />
                    </SPTabsContent>
                </SPTabs>
            </div>
        </>
    )
}

export default ProjectPage
