import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { LucideIcon, CirclePlus } from 'lucide-react'
import Image from 'next/image'

interface EmptyStateProps {
    title?: string
    description?: string
    buttonText?: string
    icon?: LucideIcon
    onClick?: () => void
    className?: string
    showButton?: boolean
}

export default function EmptyState({
    title = 'No Projects Yet',
    description = "Get started by creating your first project to manage tasks. Let's get things rolling!",
    buttonText = 'Add Project',
    showButton = true,
    onClick,
    className = '',
}: EmptyStateProps) {
    return (
        <div className={`flex flex-col items-center justify-center h-104 w-full p-6 bg-gray-50 ${className}`}>
            <div
                className="flex flex-col items-center justify-center text-center space-y-4 p-8 "
                style={{
                    backgroundImage: `url(/assets/icons/square-grid.png)`,
                    backgroundSize: 'contain',
                }}>
                <Image src={'/assets/icons/green-folder.png'} width={100} height={100} alt="folder" />

                <div className="space-y-2">
                    <h3 className="text-lg font-medium text-gray-900">{title}</h3>
                    <p className="text-sm text-gray-500 max-w-sm">{description}</p>
                </div>

                {showButton && (
                    <Button onClick={onClick} className="bg-gray-800 hover:bg-gray-700 text-white">
                        <CirclePlus className="mr-2 h-4 w-4" />
                        {buttonText}
                    </Button>
                )}
            </div>
        </div>
    )
}
