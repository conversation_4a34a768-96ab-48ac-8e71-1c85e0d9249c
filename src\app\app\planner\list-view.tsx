'use client'

import { DnDTaskType as CardType, DnDTaskType } from '@/components/kanban-dnd/types'
import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import DepartmentChip from '@/components/ui/department-chip'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import { Flame, Loader } from 'lucide-react'
import { DataTable } from '@/components/custom-data-table'
import { useRouter } from 'next/navigation'
import { formatDate } from '@/utils/format-date.utils'
import getStatusIcon from '@/components/get-status-icon'
import { ScrollArea } from '@/components/ui/scroll-area'

type DnDTaskTypeWithStringId = Omit<DnDTaskType, 'id'> & {
    id: string
}
interface ListViewProps {
    items: CardType[]
    onTaskUpdate?: (task: CardType) => Promise<void>
    isUpdating: boolean
    isLoading: boolean
}

const columns: ColumnDef<DnDTaskTypeWithStringId>[] = [
    {
        id: 'select',
        header: ({ table }) => (
            <Checkbox
                checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                id="select"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'short_code',
        header: 'Task Id',
        cell: ({ row }) => {
            return (
                <div className="flex justify-center items-center gap-2 w-fit">
                    {row.original.taskDepartment && (
                        <DepartmentChip
                            shortCode={row.original.taskDepartment.short_code}
                            label={row.original.short_code}
                            size="sm"
                        />
                    )}
                </div>
            )
        },
    },
    {
        accessorKey: 'title',
        header: 'Task Name',
        cell: ({ row }) => {
            return <div className="w-fit max-w-[350px] truncate text-sm font-medium mb-1">{row.original.title}</div>
        },
    },

    {
        accessorKey: 'taskStatus',
        header: 'Status',
        cell: ({ row }) => {
            const { label, colour } = row.original.taskStatus
            const newLabel = label
            return (
                <div className="flex items-center gap-2" style={{ color: colour }}>
                    <div>{getStatusIcon(newLabel)}</div>
                    <p>{newLabel}</p>
                </div>
            )
        },
    },
    {
        accessorKey: 'assignedUser',
        header: 'Assignee',
        cell: ({ row }) => {
            const assignees = row.original?.taskAssignees
            return (
                <div className="flex -space-x-2">
                    {assignees
                        ?.slice(0, 3)
                        ?.map((member) => (
                            <AssigneeAvatar
                                key={member.id}
                                assignee={member.first_name + ' ' + member.last_name}
                                className="border-2 border-white h-[24px] w-[24px]"
                            />
                        ))}
                    {assignees && assignees.length > 3 && (
                        <AssigneeAvatar
                            className="border-2 border-white h-[24px] w-[24px]"
                            assignee={`+ ${
                                assignees.length - 3 > 1 ? assignees.length - 3 + 'Others' : assignees.length - 3 + 'Other'
                            }`}
                        />
                    )}
                </div>
            )
        },
    },
    {
        accessorKey: 'due_date',
        header: 'Due Date',
        cell: ({ row }) => {
            const formattedDate = row.original.due_date ? formatDate(new Date(row.original.due_date), 'DD-MM-YYYY') : null

            return `${formattedDate}`
        },
    },
    {
        accessorKey: 'taskPriority',
        header: 'Priority',
        cell: ({ row }) => {
            return (
                <>
                    {row.original.taskPriority &&
                        (row.original.taskPriority.label.toLowerCase() === 'high' ? (
                            <div className="flex items-center text-sm">
                                <Flame color={row.original.taskPriority.color} size={14} />
                                <p style={{ color: row.original.taskPriority.color }}>{row.original.taskPriority.label}</p>
                            </div>
                        ) : (
                            row.original.taskPriority.label
                        ))}
                </>
            )
        },
    },
]

const ListView = ({ items, isLoading }: ListViewProps) => {
    const router = useRouter()

    return (
        <>
            {isLoading ? (
                <div className="p-4 w-full flex items-center justify-center h-[40vh]">
                    <Loader className="animate-spin" />
                </div>
            ) : items.length === 0 ? (
                <div>no data</div>
            ) : (
                <ScrollArea className="h-140">
                    <DataTable
                        columns={columns}
                        data={items.map((item) => ({ ...item, id: item.id.toString() }))}
                        // handleNextPage={handleNextPage}
                        // handlePreviousPage={handlePreviousPage}
                        onRowClick={(row) => {
                            router.push(`task/${row.id}`) // Your dynamic route
                        }}
                    />
                </ScrollArea>
            )}
        </>
    )
}

export default ListView
