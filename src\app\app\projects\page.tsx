'use client'

import { use<PERSON><PERSON>back, useEffect, useState } from 'react'
import { Search, PlusIcon, Settings2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select'

import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import ProjectsGridView from './grid-view'
import ListView from './list-view'
import { Switch } from '@/components/ui/switch'
export type ProjectCount = {
    total: number
    currentPage: number
    totalPages: number
    pageSize: number
    loaded?: number
}

export default function Page() {
    const { setBreadcrumbs } = useBreadcrumbStore()
    const [searchInput, setSearchInput] = useState('')
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
    const [viewType, setViewType] = useState<'grid' | 'list'>('grid')
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [isSwitchOn, setIsSwitchOn] = useState(false)
    const [projectCount, setProjectCount] = useState<ProjectCount | null>({
        total: 0,
        currentPage: 1,
        totalPages: 1,
        pageSize: 10,
        loaded: 0,
    })

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchQuery(searchInput)
        }, 500)
        return () => clearTimeout(timer)
    }, [searchInput])

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/' },
            { label: 'Projects', href: '/app/projects' },
        ])
    }, [setBreadcrumbs])

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchInput(e.target.value)
    }

    const handleViewChange = (value: boolean) => {
        setIsSwitchOn(value)
        setViewType(value ? 'list' : 'grid')
    }

    const handleCreateProject = () => {
        setIsModalOpen(true)
    }

    const handleCloseModal = () => {
        setIsModalOpen(false)
    }

    const handleProjectCount = useCallback((data: ProjectCount) => {
        setProjectCount(data)
    }, [])

    return (
        <>
            <div className="flex items-center justify-between w-full py-2">
                <div>
                    <p className="text-lg font-semibold">Projects</p>
                    <p className="text-xs text-[#71717A]">
                        {viewType === 'grid'
                            ? `Showing ${projectCount?.loaded} of ${projectCount?.total} ${projectCount?.total === 1 ? ' project' : ' projects'}`
                            : `Showing ${projectCount?.loaded} of ${projectCount?.total} ${projectCount?.total === 1 ? ' project' : ' projects'}`}
                    </p>
                </div>
                <div className="flex items-center gap-4 w-[60%]">
                    <div className="flex w-[200px] items-center justify-center">
                        <Switch
                            checked={isSwitchOn}
                            onCheckedChange={handleViewChange}
                            className="data-[state=checked]:bg-[#2FA87A]"
                        />
                        <p className="pl-1 text-sm font-medium text-[#020617]">
                            {`${viewType?.charAt(0)?.toUpperCase()}${viewType?.slice(1)} View`}
                        </p>
                    </div>
                    <div className="relative w-full bg-white">
                        <Search className="absolute left-2.5 top-3 h-4 w-4 text-gray-500" />
                        <Input
                            type="text"
                            placeholder="Search by name or team ..."
                            className="pl-8 h-10 focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none focus:border-slate-200"
                            value={searchInput}
                            onChange={handleSearch}
                        />
                    </div>

                    <Select value={viewType} onValueChange={() => {}}>
                        <SelectTrigger className="w-fit h-9 rounded-md border border-slate-200 bg-white [&>svg]:hidden cursor-pointer">
                            <div className="flex items-center gap-2">
                                <Settings2 />
                                {/* <SelectValue placeholder="View" />  */}
                            </div>
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem className="cursor-pointer" value="grid">
                                Grid
                            </SelectItem>
                            <SelectItem className="cursor-pointer" value="list">
                                List
                            </SelectItem>
                        </SelectContent>
                    </Select>

                    <Button className="bg-black text-white hover:bg-gray-800" onClick={handleCreateProject}>
                        Create Project <PlusIcon className="h-4 w-4" />
                    </Button>
                </div>
            </div>
            {viewType === 'grid' ? (
                <ProjectsGridView
                    debouncedSearchQuery={debouncedSearchQuery}
                    handleCloseModal={handleCloseModal}
                    isModalOpen={isModalOpen}
                    handleProjectCount={handleProjectCount}
                    handleCreateProject={handleCreateProject}
                />
            ) : (
                <ListView
                    debouncedSearchQuery={debouncedSearchQuery}
                    handleCloseModal={handleCloseModal}
                    isModalOpen={isModalOpen}
                    handleProjectCount={handleProjectCount}
                    handleCreateProject={handleCreateProject}
                />
            )}
        </>
    )
}
