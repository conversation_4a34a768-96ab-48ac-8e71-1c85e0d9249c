import { Badge } from '@/components/ui/badge'

interface PriorityBadgeProps {
    priority?: string
    className?: string
    colorMap?: {
        [key: string]: string
    }
    children?: React.ReactNode
}
//TO-DO---
const defaultColorMap: Record<string, string> = {
    high: 'bg-[#BC5090]',
    // medium: 'bg-[#494CA2]',
    medium: 'bg-white',
    low: 'bg-[#494CA2]',
    default: 'bg-[#BC5090]',
}

export const PriorityBadge: React.FC<PriorityBadgeProps> = ({
    priority = 'default',
    className = '',
    colorMap = defaultColorMap,
    children,
}) => {
    const normalizedPriority = priority.toLowerCase()
    const bgColor = colorMap[normalizedPriority] || colorMap['default']

    return <Badge className={`${bgColor} text-white ${className}`}>{children || priority}</Badge>
}
