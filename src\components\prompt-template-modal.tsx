'use client'

import React, { useState, useEffect, useRef } from 'react'
import { X, Search, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import type { JSX } from 'react/jsx-runtime'

interface PromtTemplateModalProps {
    isOpen?: boolean
    onClose?: () => void
}

type TabValue = 'onboarding' | 'homePage' | 'profile' | 'settings' | 'messaging'

interface SuggestionsData {
    [key: string]: string[]
}

export default function PromtTemplateModal({ isOpen = true, onClose = () => {} }: PromtTemplateModalProps): JSX.Element | null {
    const [activeTab, setActiveTab] = useState<TabValue>('onboarding')
    const [searchTerm, setSearchTerm] = useState<string>('')
    const [selectedSuggestion, setSelectedSuggestion] = useState<string | null>(null)
    const modalRef = useRef<HTMLDivElement>(null)

    const suggestions: SuggestionsData = {
        onboarding: ['Login Functionality', 'Sign up Functionality', 'Forgot Password Functionality', 'OTP Authentication'],
        homePage: ['Hero Section', 'Feature Showcase', 'Testimonials Section', 'Call to Action'],
        profile: ['User Profile Information', 'Edit Profile', 'Change Password', 'Privacy Settings'],
        settings: ['Account Settings', 'Notification Preferences', 'Theme Customization', 'Language Settings'],
        messaging: ['Chat Interface', 'Message Notifications', 'Group Messaging', 'Message Search'],
    }

    const getFilteredSuggestions = (category: string): string[] => {
        if (!searchTerm.trim()) return suggestions[category]
        return suggestions[category].filter((suggestion) => suggestion.toLowerCase().includes(searchTerm.toLowerCase()))
    }

    const findFirstTabWithResults = (): TabValue => {
        if (!searchTerm.trim()) return activeTab
        for (const tab of Object.keys(suggestions) as TabValue[]) {
            const filteredSuggestions = suggestions[tab].filter((suggestion) =>
                suggestion.toLowerCase().includes(searchTerm.toLowerCase()),
            )
            if (filteredSuggestions.length > 0) {
                return tab
            }
        }
        return activeTab
    }

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
        setSearchTerm(e.target.value)
        if (e.target.value.trim()) {
            const tabWithResults = findFirstTabWithResults()
            setActiveTab(tabWithResults)
        }
    }

    const handleSuggestionSelect = (suggestion: string): void => {
        setSelectedSuggestion(suggestion === selectedSuggestion ? null : suggestion)
    }

    // Close modal when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
                onClose()
            }
        }

        document.addEventListener('mousedown', handleClickOutside)
        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [onClose])

    if (!isOpen) return null

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <div ref={modalRef} className="bg-white rounded-[20px] w-full max-w-[564px] shadow-lg overflow-hidden">
                <div className="py-[12px] px-[20px]">
                    <div className="flex justify-between items-center mb-[12px]">
                        <h2 className="text-[12px] font-bold text-[#000000]">Prompt Templates</h2>
                        <button
                            onClick={onClose}
                            className="text-gray-500 hover:text-gray-700 transition-colors"
                            aria-label="Close">
                            <X className="h-[20px] w-[20px]" />
                        </button>
                    </div>

                    <div className="relative mb-[8px]">
                        <div className="absolute left-[8] top-1/2 transform -translate-y-1/2 text-gray-400">
                            <Search className="h-4 w-4" />
                        </div>
                        <Input
                            placeholder="What are you looking for?"
                            className="pl-8 h-[32px] text-sm rounded-[9px] border border-[#dee3e7] bg-white focus-visible:ring-0 focus-visible:ring-offset-0"
                            value={searchTerm}
                            onChange={handleSearchChange}
                        />
                    </div>

                    <div className="mb-[8px]">
                        <div className="flex space-x-1">
                            {(['onboarding', 'homePage', 'profile', 'settings', 'messaging'] as TabValue[]).map((tab) => (
                                <button
                                    key={tab}
                                    onClick={() => setActiveTab(tab)}
                                    className={`py-[13px] px-[16px] rounded-[8px] text-[12px] transition-colors ${
                                        activeTab === tab
                                            ? 'bg-[#EEF3FF] text-[#3730a3] font-semibold'
                                            : 'text-[#667085] hover:bg-gray-100'
                                    }`}>
                                    {tab.charAt(0).toUpperCase() + tab.slice(1)}
                                </button>
                            ))}
                        </div>

                        <div>
                            <h3 className="text-[#667085] text-[12px] mb-[8px]">Suggestions</h3>

                            <div className="space-y-3">
                                {(Object.keys(suggestions) as TabValue[]).map((tab) => (
                                    <div key={tab} className={tab === activeTab ? 'block' : 'hidden'}>
                                        {getFilteredSuggestions(tab).length > 0 ? (
                                            getFilteredSuggestions(tab).map((suggestion, index) => (
                                                <div
                                                    key={index}
                                                    className={`flex items-center gap-[6px] p-[8px] rounded-[8px] cursor-pointer transition-colors ${
                                                        selectedSuggestion === suggestion
                                                            ? 'bg-[#f6f6f6]'
                                                            : 'bg-transparent hover:bg-[#f6f6f6]/50'
                                                    }`}
                                                    onClick={() => handleSuggestionSelect(suggestion)}>
                                                    <FileText className="text-[#3b3c3f] h-[16px] w-[16px]" />
                                                    <span className="text-[#3b3c3f] text-[12px] font-medium">{suggestion}</span>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="text-center py-4 text-[#667085] text-[12px] font-medium">
                                                No matching suggestions found
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    <div className="mt-[18px]">
                        <Button
                            className="w-full py-6 bg-[#030712] hover:bg-[#141414] text-white rounded-lg text-base font-medium"
                            onClick={() => {
                                if (selectedSuggestion) {
                                    onClose()
                                }
                            }}
                            disabled={!selectedSuggestion}>
                            Confirm
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
}
