import React from 'react'
import TaskDistributionGraph from './task-distribution-graph'

interface LayoutProps {
    children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
    return (
        <div className="flex flex-row w-full ">
            <div className="flex flex-row w-2/3 h-full border-r pr-1">{children}</div>
            <div className="flex h-fit flex-row w-1/3 border">
                <TaskDistributionGraph />
            </div>
        </div>
    )
}

export default Layout
