// components/Dashboard.tsx
import React from 'react'
import Image from 'next/image'

interface TeamMember {
    id: number
    avatar: string
}

interface DashboardProps {
    projectName: string
    taskStatusDescription: string
    techStack: string[]
    teamMembers: TeamMember[]
    totalTeamMembers: number
    featuresCompleted: number
    totalFeatures: number
    createdDate: string
    updatedDate: string
}

const ProjectDetailsSP: React.FC<DashboardProps> = ({
    projectName,
    taskStatusDescription,
    teamMembers,
    totalTeamMembers,
    featuresCompleted,
    totalFeatures,
    createdDate,
    updatedDate,
}) => {
    return (
        <div className="mt-[15px] max-w-3xl">
            <div className="flex items-center mb-6">
                <div className="w-[32px] h-[32px] p-[7px] bg-white rounded-[8px] flex items-center justify-center border border-[#E8E8ED] mr-2">
                    <Image src="/assets/avatars/project1.png" alt="Project Icon" width={20} height={20} />
                </div>
                <h1 className="text-lg font-bold">{projectName}</h1>
            </div>

            <div className="mb-6">
                <p className="text-gray-500 text-sm">{taskStatusDescription}</p>
            </div>

            <div className="grid grid-cols-2 gap-6 mb-4">
                <h2 className="text-gray-500 text-sm font-semibold mb-2">Tech Stack</h2>
                <div>
                    <div className="flex items-center">
                        {/* <div className="flex -space-x-2 mr-2">
                            {teamMembers.map((member) => (
                                <div
                                    key={member.id}
                                    className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white overflow-hidden">
                                    <Image
                                        src={member.avatar}
                                        alt="Team member avatar"
                                        width={32}
                                        height={32}
                                        className="object-cover w-full h-full"
                                    />
                                </div>
                            ))}
                            {teamMembers.length < totalTeamMembers && (
                                <div className="w-8 h-8 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-xs text-gray-500">
                                    +{totalTeamMembers - teamMembers.length}
                                </div>
                            )}
                        </div>
                        <span className="text-gray-500">{totalTeamMembers} members</span> */}
                    </div>
                </div>
            </div>
            <div className="grid grid-cols-2 gap-6 mb-4">
                <h2 className="text-gray-500 text-sm font-semibold mb-2">Team Members</h2>
                <div>
                    <div className="flex items-center h-[24px]">
                        <div className="flex -space-x-2 mr-2">
                            {teamMembers.map((member) => (
                                <div
                                    key={member.id}
                                    className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white overflow-hidden">
                                    <Image
                                        src={member.avatar}
                                        alt="Team member avatar"
                                        width={32}
                                        height={32}
                                        className="object-cover w-full h-full"
                                    />
                                </div>
                            ))}
                            {teamMembers.length < totalTeamMembers && (
                                <div className="w-8 h-8 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-[10px] text-gray-500">
                                    +{totalTeamMembers - teamMembers.length}
                                </div>
                            )}
                        </div>
                        <span className="text-xs text-gray-500 ">{totalTeamMembers} members</span>
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-2 gap-4 ">
                <h2 className="text-gray-500 text-sm font-semibold mb-2">Features</h2>
                <p className="text-sm text-gray-700">
                    {featuresCompleted} / {totalFeatures} completed
                </p>
                <h2 className="text-gray-500 text-sm font-semibold mb-2">Created</h2>
                <p className="text-sm text-gray-700">{createdDate}</p>
                <h2 className="text-gray-500 text-sm font-semibold mb-2">Updated</h2>
                <p className="text-sm text-gray-700">{updatedDate}</p>
            </div>
        </div>
    )
}

export default ProjectDetailsSP
