'use client'

import * as React from 'react'
import { AudioWaveform, ChevronsUpDown, Plus, UserRoundPlus } from 'lucide-react'

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from '@/components/ui/sidebar'
import { Avatar, AvatarImage } from './ui/avatar'
import { useAuthStore, Workspace } from '@/store/auth.store'
import CreateWorkspaceModal from './create-workspace-modal'
import { TeamInviteModal } from './team-invite-modal'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { AxiosError } from 'axios'
import { usePathname, useRouter } from 'next/navigation'

const exitFromPathsOnWorkspaceSwitch = [
    {
        from: 'app/task/', // it contains dynamic id after /
        to: '/app/planner',
    },
]

export function TeamSwitcher({
    teams,
}: {
    teams: {
        id: number
        name: string
        img_url: string
        plan: number
        invite_link: string
    }[]
}) {
    const { isMobile } = useSidebar()
    const currentWorkspace = useAuthStore((state) => state.currentWorkspace)
    const setCurrentWorkSpace = useAuthStore((state) => state.setCurrentWorkSpace)
    const [modalOpen, setModalOpen] = React.useState(false)
    const [inviteModalOpen, setInviteModalOpen] = React.useState(false)
    const [inviteLink, setInviteLink] = React.useState('')
    const [clickedWorkspace, setClickedWorkspace] = React.useState<number | null>(null)
    const [isEmailSubmit, setIsEmailSubmit] = React.useState(false)
    const router = useRouter()
    const currentPath = usePathname()

    const clearAllStates = () => {
        setModalOpen(false)
        setInviteModalOpen(false)
        setInviteLink('')
        setClickedWorkspace(null)
        setIsEmailSubmit(false)
    }

    const onAfterInviteSuccess = () => {
        setInviteLink('')
        toast.success('Invitation has been sent via email.')
        clearAllStates()
    }

    const inviteMutation = useMutation({
        mutationFn: async (data: { emails?: string[]; link?: string; action: string }) => {
            const url = endpoints.workspace.invite.replace(':id', clickedWorkspace?.toString() || '')
            const response = await api.post(url, data)
            return response.data
        },
        onSuccess: (data) => {
            setInviteLink(data.invite_link)
            if (isEmailSubmit) {
                onAfterInviteSuccess()
                return
            }
            setInviteModalOpen(true)
        },
        onError: (error: AxiosError) => {
            const errorText = (error.response?.data as { error?: string })?.error
            axiosErrorToast(error, errorText || 'Failed to send invite. Please try again.')
        },
    })

    const handleInvite = async (action: string, emails?: string[], link?: string) => {
        await inviteMutation.mutate({ emails, link, action })
    }

    const handleSubmitForm = (emails: string[]) => {
        setIsEmailSubmit(true)
        handleInvite('email', emails)
    }
    const handleWorkspaceSwitch = (workspace: Workspace) => {
        setCurrentWorkSpace(workspace)
        exitFromPathsOnWorkspaceSwitch.map((path) => {
            if (currentPath.includes(path.from)) {
                router.push(path.to)
            }
        })
    }

    return (
        <SidebarMenu>
            <SidebarMenuItem>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            size="lg"
                            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
                            {currentWorkspace?.img_url ? (
                                <div className=" text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                                    <Avatar className="h-8 w-8 rounded-lg">
                                        {currentWorkspace?.img_url && (
                                            <AvatarImage src={currentWorkspace?.img_url} alt={currentWorkspace?.name} />
                                        )}
                                    </Avatar>
                                </div>
                            ) : (
                                <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                                    <AudioWaveform className=" bg-sidebar-primary size-4" />
                                </div>
                            )}
                            <div className="grid flex-1 text-left text-sm leading-tight">
                                <span className="truncate font-medium">{currentWorkspace?.name}</span>
                                <span className="truncate text-xs">
                                    {/* {currentWorkspace?.plan} */}
                                    Free Trial - 5 days left
                                </span>
                            </div>
                            <ChevronsUpDown className="ml-auto" />
                        </SidebarMenuButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                        className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
                        align="start"
                        side={isMobile ? 'bottom' : 'right'}
                        sideOffset={4}>
                        <DropdownMenuLabel className="text-muted-foreground text-xs">Workspaces</DropdownMenuLabel>
                        <div className="max-h-[300px] overflow-auto custom-scroll">
                            {teams.map((team) => (
                                <DropdownMenuItem
                                    key={team.id}
                                    className={`gap-2 p-0 m-1 ${team?.id === currentWorkspace?.id && 'bg-gray-100'}`}>
                                    <div
                                        onClick={() => handleWorkspaceSwitch(team)}
                                        className="flex items-center p-2 gap-2 w-full h-fit">
                                        <div className="flex size-6 items-center justify-center rounded-xs border">
                                            {team?.img_url ? (
                                                <Avatar className="h-8 w-8 rounded-lg">
                                                    {team?.img_url && <AvatarImage src={team?.img_url} alt={team?.name} />}
                                                </Avatar>
                                            ) : (
                                                <AudioWaveform className="size-4 shrink-0" />
                                            )}
                                        </div>
                                        {team.name}
                                    </div>
                                    <DropdownMenuShortcut className="pr-2">
                                        <div
                                            className="border p-2 rounded-full cursor-pointer "
                                            onClick={() => {
                                                setClickedWorkspace(team.id)
                                                handleInvite('copy', undefined, undefined)
                                            }}>
                                            <UserRoundPlus className="size-4 shrink-0" />
                                        </div>
                                    </DropdownMenuShortcut>
                                </DropdownMenuItem>
                            ))}
                        </div>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="gap-2 p-2" onClick={() => setTimeout(() => setModalOpen(true), 100)}>
                            <div className="bg-background flex size-6 items-center justify-center rounded-md border">
                                <Plus className="size-4" />
                            </div>
                            <div className="text-muted-foreground font-medium">Add workspace</div>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
                <CreateWorkspaceModal
                    open={modalOpen}
                    onOpenChange={() => {
                        setModalOpen(false)
                        clearAllStates()
                    }}
                />
                <TeamInviteModal
                    showTrigger={false}
                    isOpen={inviteModalOpen}
                    onOpenChange={() => {
                        setInviteModalOpen(false)
                        clearAllStates()
                    }}
                    defaultLink={inviteLink}
                    onInvite={(emails) => handleSubmitForm(emails)}
                    isLoading={inviteMutation.isPending}
                />
            </SidebarMenuItem>
        </SidebarMenu>
    )
}
