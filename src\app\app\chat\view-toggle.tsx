'use client'
import { LayoutDashboardIcon, MessageSquareIcon } from 'lucide-react'

interface ViewToggleProps {
    currentView: 'chat' | 'project'
    onToggle: (view: 'chat' | 'project') => void
}

export const ViewToggle = ({ currentView, onToggle }: ViewToggleProps) => {
    return (
        <div>
            <div className="flex">
                <button
                    onClick={() => onToggle('project')}
                    className={`p-2 rounded-full flex items-center justify-center transition-all ${
                        currentView === 'project'
                            ? 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
                            : 'text-gray-500 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800'
                    }`}
                    aria-label="Switch to Project View"
                    title="Project Planner">
                    <LayoutDashboardIcon className="h-4 w-4" />
                </button>
                <button
                    onClick={() => onToggle('chat')}
                    className={`p-2 rounded-full flex items-center justify-center transition-all ${
                        currentView === 'chat'
                            ? 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
                            : 'text-gray-500 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800'
                    }`}
                    aria-label="Switch to Chat View"
                    title="Chat">
                    <MessageSquareIcon className="h-4 w-4" />
                </button>
            </div>
        </div>
    )
}
