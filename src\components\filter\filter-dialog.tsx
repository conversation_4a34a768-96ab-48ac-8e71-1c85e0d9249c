'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { FilterContainer } from './filter-container'
import { useFilterStore } from '@/store/filter-store'
import { Settings2 } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import type { FilterCategory } from './types'

interface FilterDialogProps {
    categories: FilterCategory[]
    onFiltersChange?: (filters: Record<string, number[]>) => void
    defaultApiEndpoint?: string
}

export function FilterDialog({ categories, onFiltersChange, defaultApiEndpoint = '/api/filters' }: FilterDialogProps) {
    const { isDialogOpen, setDialogOpen, getTotalSelectedCount, selectedFilters } = useFilterStore()
    // Use client-side state to avoid hydration mismatch
    const [totalCount, setTotalCount] = useState(0)

    // Update the count on the client side only
    useEffect(() => {
        setTotalCount(getTotalSelectedCount())
    }, [selectedFilters, getTotalSelectedCount])

    useEffect(() => {
        if (onFiltersChange) {
            onFiltersChange(selectedFilters)
        }
    }, [selectedFilters, onFiltersChange])

    return (
        <Popover open={isDialogOpen} onOpenChange={setDialogOpen}>
            <PopoverTrigger asChild className="h-full rounded-sm">
                <Button variant="outline" className="gap-2 h-full">
                    <Settings2 className="h-4 w-4" />
                    <span>Filters</span>
                    {totalCount > 0 && (
                        <Badge variant="secondary" className="ml-1">
                            {totalCount}
                        </Badge>
                    )}
                </Button>
            </PopoverTrigger>

            <PopoverContent className="w-[665px] h-[384px] p-0" sideOffset={10} align="center">
                <FilterContainer categories={categories} defaultApiEndpoint={defaultApiEndpoint} />
            </PopoverContent>
        </Popover>
    )
}
