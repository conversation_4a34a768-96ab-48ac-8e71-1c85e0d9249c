'use client'

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import TaskBottomTabs from './task-components/bottom-tab'
import DeadlineAlert from './task-components/deadline-alert'
import DependentTaskAccordian from './task-components/dependent-task-accordian'
import TaskDetailsCard from './task-components/task-details-card'
import { useQuery } from '@tanstack/react-query'
import endpoints from '@/services/api-endpoints'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { Loader } from 'lucide-react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import MarkdownRenderer from '@/components/markdown-renderer'
import { getDateDifference } from '@/utils/get-date-difference.utils'
import { Badge } from '@/components/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useEffect, useState } from 'react'
import { useBreadcrumbStore } from '@/store/breadcrumb.store'

export default function TaskPage() {
    const params = useParams()
    const taskId = Number(params.id)
    const router = useRouter()
    const { setBreadcrumbs } = useBreadcrumbStore()

    const [linkCopied, setLinkCopied] = useState(false)

    const copyShortCode = (short_code: string) => {
        navigator.clipboard.writeText(short_code || '')
        setLinkCopied(true)
        setTimeout(() => setLinkCopied(false), 3000)
        toast.success('Copied to clipboard!')
    }

    const {
        data: task,
        isLoading,
        refetch: refetchTask,
    } = useQuery({
        queryKey: ['task', taskId],
        queryFn: async () => {
            try {
                const response = await api.get(`${endpoints.tasks.getTasks}/${taskId}`)
                return response.data.data
            } catch (error) {
                toast.error('Failed to fetch task details')
                router.push('/app/planner')
                throw error
            }
        },
        enabled: !!taskId,
    })

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/app' },
            { label: 'Tasks', href: '/app/planner' },
            { label: task?.short_code || 'task Name', href: `/app/task/${taskId}` },
        ])
    }, [setBreadcrumbs, task?.short_code, taskId])

    const taskStatus = task?.taskStatus.label
    const taskDueDate = task?.due_date
    const taskDueInfo = getDateDifference(taskDueDate)

    const showDeadlineAlert = taskDueInfo && taskStatus?.toLowerCase() !== 'complete' && taskDueDate && taskDueInfo?.daysLeft <= 2

    if (isLoading)
        return (
            <div className="flex items-center justify-center h-[88dvh]">
                <Loader className="animate-spin" />
            </div>
        )

    return (
        <ScrollArea className="h-[90dvh] " type="scroll">
            <div className="w-full space-y-2">
                {showDeadlineAlert && (
                    <DeadlineAlert
                        title={`You're Almost There! Deadline ${taskDueInfo?.label}`}
                        description={`Great progress! You're in the final stretch. Remember, your task deadline is ${taskDueInfo?.label}. Review your tasks and let's finish strong!`}
                        className="bg-gradient-to-r from-[#F3F3F3] via-[#F4F5F6] to-[#F8F8F8] text-white p-4"
                    />
                )}
                <div className="flex gap-4">
                    <div className="w-full space-y-2">
                        <button
                            onClick={() => copyShortCode(task.short_code)}
                            className={linkCopied ? 'opacity-50 cursor-not-allowed' : 'cursor-copy'}
                            disabled={linkCopied}>
                            <Badge className="bg-[#00D0819A] rounded-sm ">{task.short_code}</Badge>
                        </button>
                        {task?.dependentTasks?.length > 0 && <DependentTaskAccordian dependentTasks={task?.dependentTasks} />}
                        <Card className="gap-0 p-2 rounded-[10px] shadow-none border-dashed">
                            <CardHeader className="font-semibold text-lg">{task?.title}</CardHeader>
                            <CardContent className="text-sm text-[#71717A]">
                                <MarkdownRenderer content={task?.description} allowCopy={false} enableExpansion={false} />
                            </CardContent>
                        </Card>
                    </div>
                    {task && <TaskDetailsCard taskData={task} refetchTask={refetchTask} />}
                </div>
                <TaskBottomTabs />
            </div>
        </ScrollArea>
    )
}
