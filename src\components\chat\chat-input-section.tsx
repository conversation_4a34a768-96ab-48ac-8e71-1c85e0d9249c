import { motion } from 'framer-motion'
import { ZapIcon } from 'lucide-react'
import { ChatInput as BaseChatInput } from '@/components/chat/chat-input'

interface ChatInputSectionProps {
    onSend: (message: string) => void
    isLoading: boolean
    promptCount?: number
    promptLimit?: number
    onViewToggle?: (view: 'chat' | 'project') => void
    currentView?: 'chat' | 'project'
}

export const ChatInputSection = ({
    onSend,
    isLoading,
    promptCount = 250,
    promptLimit = 250,
    onViewToggle,
    currentView,
}: ChatInputSectionProps) => {
    return (
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.6 }} className="mt-6">
            <div className="border border-gray-100 dark:border-gray-700 rounded-xl px-0 py-2 dark:bg-gray-900">
                <BaseChatInput onSend={onSend} isLoading={isLoading} onViewToggle={onViewToggle} currentView={currentView} />
            </div>
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
                className="mt-2 flex items-center text-xs p-4 text-gray-500 dark:text-gray-400">
                <ZapIcon className="h-4 w-4 mr-2" />
                <span>
                    {promptCount}/{promptLimit} Prompts
                </span>
            </motion.div>
        </motion.div>
    )
}
