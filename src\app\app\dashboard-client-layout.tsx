'use client'

import { ReactNode } from 'react'
import { AppSidebar } from '@/components/app-sidebar'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar'

import { usePathname } from 'next/navigation'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useTabStore } from '@/store/tabs.store'
import Image from 'next/image'
import { Bell, GalleryVerticalEnd } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useBreadcrumbStore } from '@/store/breadcrumb.store'

const tabItems = [
    {
        value: 'chat',
        label: 'Chat',
        activeIcon: <Image alt="" src="/assets/icons/chat-active.svg" width={16} height={16} />,
        inactiveIcon: <Image alt="" src="/assets/icons/chat-inactive.svg" width={16} height={16} />,
    },
    {
        value: 'history',
        label: 'History',
        activeIcon: <GalleryVerticalEnd fill="#2A2A2A" />,
        inactiveIcon: <GalleryVerticalEnd color="#B5B5B5" fill="#B5B5B5" />,
    },
]

export default function ClientLayout({ children, defaultOpen }: { children: ReactNode; defaultOpen: boolean }) {
    const pathname = usePathname()
    const activeTab = useTabStore((state) => state.activeTab)
    const setActiveTab = useTabStore((state) => state.setActiveTab)

    const handleTabChange = (tab: string) => {
        setActiveTab(tab)
        // router.push(tab)
    }

    const renderHeader = () => {
        if (pathname.includes('/chat') || pathname === '/app') {
            return (
                <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear">
                    <div className="flex items-center gap-2 px-4 w-full">
                        <SidebarTrigger className="-ml-1" />
                        <Separator orientation="vertical" className="mr-2 h-4" />
                        <div className="flex justify-between bg-transparent p-4  w-full mt-2">
                            <div className="w-1/3">
                                <Tabs value={activeTab} onValueChange={handleTabChange}>
                                    <TabsList className=" bg-transparent border">
                                        {tabItems.map((tab) => (
                                            <TabsTrigger
                                                key={tab.value}
                                                value={tab.value}
                                                className="data-[state=active]:bg-[#E5E7EB] data-[state=active]:text-[#2A2A2A] text-[#B5B5B5]  px-4 py-2 rounded-md">
                                                {activeTab === tab.value ? tab.activeIcon : tab.inactiveIcon}
                                                {tab.label}
                                            </TabsTrigger>
                                        ))}
                                    </TabsList>
                                </Tabs>
                            </div>
                            <div className="flex justify-center items-center w-1/3">
                                <Image
                                    src="/assets/img/raydian-logo.png"
                                    alt="Logo"
                                    width={27}
                                    height={22}
                                    className="object-contain"
                                />
                                <div className="text-sm pb-2 pl-2">Raydian</div>
                            </div>
                            <div className="flex justify-center items-center w-1/3">
                                <Button variant="outline">
                                    <Bell />
                                </Button>
                            </div>
                        </div>
                    </div>
                </header>
            )
        }

        return (
            <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
                <div className="flex items-center gap-2 px-4">
                    <SidebarTrigger className="-ml-1" />
                    <Separator orientation="vertical" className="mr-2 h-4" />
                    <BreadcrumbComponent />
                </div>
            </header>
        )
    }

    return (
        <SidebarProvider defaultOpen={defaultOpen}>
            <AppSidebar />
            <SidebarInset className="overflow-hidden">
                {renderHeader()}
                {children}
            </SidebarInset>
        </SidebarProvider>
    )
}

function BreadcrumbComponent() {
    const { breadcrumbs } = useBreadcrumbStore()

    return (
        <Breadcrumb>
            <BreadcrumbList>
                {breadcrumbs.map((crumb, index) => (
                    <div key={index} className="flex flex-row items-center gap-4">
                        <BreadcrumbItem className="hidden md:block">
                            <BreadcrumbLink href={crumb.href}>{crumb.label}</BreadcrumbLink>
                        </BreadcrumbItem>
                        {index < breadcrumbs.length - 1 && <BreadcrumbSeparator className="hidden md:block" />}
                    </div>
                ))}
            </BreadcrumbList>
        </Breadcrumb>
    )
}
