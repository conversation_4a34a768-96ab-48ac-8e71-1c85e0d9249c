'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'

import { ScrollArea } from '@/components/ui/scroll-area'
import { ExternalLink, Lightbulb, Loader, Puzzle, Trash2 } from 'lucide-react'
import { formatDate } from '@/utils/format-date.utils'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import AvatarGroup from '@/components/avatar-group'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import { Card, CardContent } from '@/components/ui/card'
import { HorizontalBarChart } from '@/components/horizontal-barchart'
import { DeleteWithAlert } from '@/components/delete-with-alert-dialog'
import MarkdownRenderer from '@/components/markdown-renderer'

interface FeatureDetailsDrawerProps {
    children: React.ReactNode
    featureId: number
    refetchFeaturesOnTab: () => void
}

interface FeatureDetails {
    id: number
    feat_code: string
    title: string
    description: string
    status: {
        id: number
        label: string
        color: string
    }
    createdAt: string
    updatedAt: string
    creator: {
        id: number
        first_name: string
        last_name: string
        img_url?: string
    }
    project: {
        id: number
        name: string
    }
    tasks: {
        total: number
        completed: number
    }
    draft: boolean
    team: Array<{
        id: number
        first_name: string
        last_name: string
        img_url?: string
    }>
    taskStatus: Array<{
        status: string
        count: number
        fill: string
    }>
    progressSummary: string
    featureSummary: string
}

const feature: FeatureDetails = {
    id: 201,
    feat_code: 'FEAT-001',
    title: 'User Authentication Module',
    description: 'Handles user login, registration, and authentication flow using JWT.',
    status: {
        id: 1,
        label: 'In Progress',
        color: '#FACC15', // e.g., amber-400
    },
    createdAt: '2025-05-20T10:00:00.000Z',
    updatedAt: '2025-05-26T14:45:00.000Z',
    creator: {
        id: 1,
        first_name: 'Joel',
        last_name: 'Joy',
        img_url: 'https://example.com/images/joel.jpg',
    },
    project: {
        id: 1,
        name: 'Project Alpha',
    },
    tasks: {
        total: 6,
        completed: 3,
    },
    draft: false,
    team: [
        {
            id: 2,
            first_name: 'Albert',
            last_name: 'Ben',
            img_url: 'https://example.com/images/albert.jpg',
        },
        {
            id: 3,
            first_name: 'Sofia',
            last_name: 'Thomas',
            img_url: 'https://example.com/images/sofia.jpg',
        },
    ],
    taskStatus: [
        { status: 'Completed', count: 5, fill: '#08B38B' },
        { status: 'Backlog', count: 4, fill: '#D930259E' },
        { status: 'In Progress', count: 3, fill: '#D9770647' },
        { status: 'Review', count: 2, fill: '#7E7E7E' },
        { status: 'Bugs', count: 1, fill: '#FFD4D4' },
    ],
    progressSummary:
        'Most tasks are in In Progress, followed by Completed. Feature is on track but has 6 unresolved bugs. 2 tasks are in Review, signaling QA involvement.',
    featureSummary: `# AI Project Summary Assistant

You're a helpful AI assistant that makes project data easy to understand! 😊 

**Your mission:** Turn complex project information into clear, friendly summaries that anyone can quickly grasp.

## What to focus on:
- **The big picture** - what's happening overall?
- **Key highlights** - wins, progress, important updates
- **Things to watch** - potential roadblocks or concerns  
- **Next steps** - what actions make sense?

## Your style:
- Keep it conversational and warm
- Use simple language (avoid jargon when possible)
- Highlight the most important stuff first
- Add a friendly emoji here and there 😄
- Use clean markdown formatting
- Stay professional but approachable

## Structure your summaries like:
- Start with the main takeaway
- Break down key points clearly
- End with actionable insights or recommendations

**Remember:** You're helping busy people quickly understand what matters most. Make it easy to scan, helpful to read, and pleasant to receive! ✨`,
}

export function FeatureDetailsDrawer({ children, featureId, refetchFeaturesOnTab }: FeatureDetailsDrawerProps) {
    const [open, setOpen] = useState(false)

    const { isLoading } = useQuery({
        queryKey: ['feature', featureId],
        queryFn: async () => {
            const response = await api.get(`${endpoints.features.getFeatures}/${featureId}`)
            return response.data.data as FeatureDetails
        },
        enabled: open && !!featureId,
    })

    const handleOpenChange = (newOpen: boolean) => {
        setOpen(newOpen)
    }

    return (
        <Dialog open={open} onOpenChange={handleOpenChange} modal>
            <DialogTrigger asChild className="cursor-pointer">
                {children}
            </DialogTrigger>
            <DialogContent
                className={cn(`sm:max-w-lg h-full max-h-[90vh] p-0
                gap-0 top-10 translate-x-50 translate-y-0 rounded-lg 
                border data-[state=open]:slide-in-from-right 
                data-[state=closed]:slide-out-to-right`)}>
                <DialogHeader className="px-6 pt-0 h-fit border-b pb-2">
                    <DialogTitle className="flex text-xl items-center justify-between w-full pr-4 mt-2">
                        <div className="flex gap-2 items-center">
                            <div className="border rounded-md p-2">
                                <Puzzle size={12} color="#9BA5B1" />
                            </div>
                            <p className=" text-[18px] font-semibold">{feature?.title}</p>
                        </div>
                        <div>
                            <DeleteWithAlert
                                endpoint={`${endpoints.features.deleteFeature}/${featureId}`}
                                description="This will delete the feature and all associated tasks."
                                onAfterSuccess={refetchFeaturesOnTab}>
                                <Trash2 size={14} color="#9B9B9B" />
                            </DeleteWithAlert>
                        </div>
                    </DialogTitle>
                    <DialogDescription />
                    <div className="w-fit">
                        <Link href={{ pathname: '/app/planner', query: { featureId: featureId } }}>
                            <div className="flex items-center justify-center gap-2 text-[#08B38B]">
                                <span className="text-[12px] font-medium">View in Planner</span>
                                <ExternalLink height={14} width={14} />
                            </div>
                        </Link>
                    </div>
                </DialogHeader>

                <ScrollArea className="h-[calc(100vh-10rem)] p-4">
                    {isLoading ? (
                        <div className="flex h-full items-center justify-center">
                            <Loader className="h-6 w-6 animate-spin text-gray-400" />
                        </div>
                    ) : feature ? (
                        <div className="space-y-6 pb-6">
                            <div className="flex items-center gap-24 text-xs text-[#3C557A]">
                                <p>Tasks</p>
                                <p className=" text-xs text-[#3C557A]">
                                    {feature?.tasks?.completed}/{feature?.tasks?.total} completed
                                </p>
                            </div>
                            {feature?.team?.length > 0 && (
                                <div className="flex items-center gap-10 text-xs text-[#3C557A]">
                                    <p>Team Members</p>
                                    {<AvatarGroup team={feature?.team} />}
                                </div>
                            )}
                            <div className="flex items-center gap-16 text-xs text-[#3C557A]">
                                <p>Created By</p>
                                <div className="flex items-center gap-2">
                                    {
                                        <AssigneeAvatar
                                            assignee={feature?.creator.first_name + ' ' + feature?.creator.last_name}
                                            imageUrl={feature?.creator.img_url}
                                        />
                                    }
                                    <p>
                                        {feature?.creator.first_name} {feature?.creator.last_name}
                                    </p>
                                    <p>on {formatDate(feature?.createdAt, 'ddMonYYYY', { showTime: true, timeFormat: '12h' })}</p>
                                </div>
                            </div>
                            <div>
                                <Card className="bg-gradient-to-r from-[#113355DE] via-[#10B981EF] to-[#0D9488] text-white p-4">
                                    <CardContent className="p-0">
                                        <div className="flex items-center gap-2 text-[15px] font-semibold ">
                                            Progress Summary <Lightbulb size={16} />
                                        </div>
                                        <p className="text-[13px]">{feature?.progressSummary}</p>
                                    </CardContent>
                                </Card>
                            </div>
                            <div>
                                <div className="text-[16px] font-semibold text-[#1C2024]">Task Status Distribution</div>
                                <div className="text-xs text-[#60646C]">Across all tasks in this feature</div>
                                <HorizontalBarChart
                                    chartData={feature?.taskStatus}
                                    dataKey="count"
                                    labelKey="status"
                                    barColor="#10B981" // Tailwind green-500
                                    height={250}
                                />
                            </div>

                            <div className="space-y-4">
                                {feature?.featureSummary && (
                                    <div>
                                        <div className="text-lg text-[#60646C]">Feature Summary</div>
                                        <MarkdownRenderer content={feature?.featureSummary} enableExpansion={false} />
                                    </div>
                                )}
                            </div>
                        </div>
                    ) : (
                        <div className="flex h-full items-center justify-center">
                            <p className="text-gray-500">Feature not found</p>
                        </div>
                    )}
                </ScrollArea>
            </DialogContent>
        </Dialog>
    )
}
