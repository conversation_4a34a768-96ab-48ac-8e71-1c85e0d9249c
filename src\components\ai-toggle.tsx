'use client'

import { useState } from 'react'
import { ChevronDown, Stars } from 'lucide-react'

import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu'

interface AgentToggleProps {
    currentView: string | 'Insights' | 'Task Generator'
    handleAgentToggle: (agent: 'Insights' | 'Task Generator') => void
}

export default function AiAgentToggle({ currentView, handleAgentToggle }: AgentToggleProps) {
    const [isMenuOpen, setIsMenuOpen] = useState(false)
    const [selectedAgent, setSelectedAgent] = useState(currentView)
    const handleSelectAgent = (agent: string) => {
        setIsMenuOpen(false) // Close dropdown first
        setSelectedAgent(agent)
        handleAgentToggle(agent as 'Insights' | 'Task Generator')
    }

    return (
        <>
            <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                <DropdownMenuTrigger className="flex items-center gap-1 text-sm text-[#585858] focus:outline-none ">
                    <Stars className="h-3 w-3 text-[#585858]" />
                    {selectedAgent}
                    <ChevronDown className="h-3 w-3 text-[#585858]" />
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                    <DropdownMenuItem onClick={() => handleSelectAgent('Task Generator')}>Task Generator</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleSelectAgent('Insights')}>Insights</DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </>
    )
}
