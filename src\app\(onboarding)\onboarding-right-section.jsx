'use client'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import TestimonialSlider from '@/components/ui/onboarding-testimonial-slider'

const testimonials = [
    {
        quote: "We've been using <PERSON><PERSON> to kick start every new project and can't imagine working without it.",
        author: '<PERSON><PERSON><PERSON>',
        position: 'Lead Designer, Layers',
        company: 'Web Development Agency',
    },
    {
        quote: "<PERSON><PERSON> has transformed our workflow and improved our team's productivity by 40%.",
        author: '<PERSON>',
        position: 'CTO, DevStack',
        company: 'Software Solutions',
    },
    {
        quote: "The most intuitive platform we've used. Our clients love the results. Our clients love the results.",
        author: '<PERSON>',
        position: 'Creative Director',
        company: 'Digital Innovations',
    },
]

export default function OnboardingRightSection() {
    const pathname = usePathname()

    switch (pathname) {
        case '/invite-team':
            return <InviteTeamRightSection />
        default:
            return <DefaultRightSection />
    }
}

function DefaultRightSection() {
    return (
        <div className="relative w-full h-full ">
            <Image
                src={'/assets/img/create-workspace-right-image.png'}
                fill
                priority
                alt="Create Workspace Right Section"
                className="object-cover object-top"
                quality={90}
            />

            <div className="absolute z-10 bottom-0">
                <TestimonialSlider testimonials={testimonials} className="pl-8" />
            </div>
        </div>
    )
}

function InviteTeamRightSection() {
    return (
        <div className="relative w-full h-full ">
            <div className="absolute z-10 top-[10%] left-[30%] max-w-[300px]  text-white ">
                <div className="  text-[22px] font-medium">Teams That Sync, Win.</div>
                <div className="  text-sm text-[#C9C9C9]">
                    Bring your collaborators in early. With shared visibility and clear roles, projects gain momentum from day one
                    — not day ten.
                </div>
            </div>
            <Image
                src={'/assets/img/invite-right-image.png'}
                fill
                priority
                alt="Invite Team Right Section"
                className="object-cover"
                quality={90}
            />
        </div>
    )
}
