'use client'
import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface Testimonial {
    quote: string
    author: string
    position: string
    company: string
}

interface TestimonialSliderProps {
    testimonials: Testimonial[]
    autoPlayInterval?: number
    className?: string
}

const TestimonialSlider: React.FC<TestimonialSliderProps> = ({ testimonials, autoPlayInterval = 5000, className = '' }) => {
    const [currentIndex, setCurrentIndex] = useState(0)

    useEffect(() => {
        if (autoPlayInterval <= 0 || testimonials.length <= 1) return

        const interval = setInterval(() => {
            setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
        }, autoPlayInterval)

        return () => clearInterval(interval)
    }, [autoPlayInterval, testimonials.length])

    const goToPrevious = () => {
        setCurrentIndex((prevIndex) => (prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1))
    }

    const goToNext = () => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
    }

    return (
        <div
            className={`bg-gradient-to-b from-[rgba(0,0,0,0)] to-[rgba(0,0,0,0.1)] h-full flex flex-col justify-end ${className}`}>
            <div className="w-full">
                <div className="flex-grow flex flex-col justify-center h-[265px]">
                    <div className="flex justify-between items-center mt-8 pb-8">
                        <AnimatePresence mode="wait">
                            <motion.div
                                key={currentIndex}
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                transition={{ duration: 0.5, ease: 'easeInOut' }}>
                                <blockquote className="h-[90px] text-3xl text-white font-medium mb-8 max-w-2xl leading-[44px]">
                                    &quot;{testimonials[currentIndex].quote}&quot;
                                </blockquote>

                                <div className="pb-8 pt-6">
                                    <p className="text-white text-[30px] font-semibold text-lg leading-[28px] pb-4">
                                        {testimonials[currentIndex].author}
                                    </p>
                                    <p className="text-white font-medium text-lg leading-[24px]">
                                        {testimonials[currentIndex].company}
                                    </p>
                                    <p className="text-white font-medium text-lg leading-[24px]">
                                        {testimonials[currentIndex].position}
                                    </p>
                                </div>
                            </motion.div>
                        </AnimatePresence>

                        <div className="flex space-x-4 absolute right-20 bottom-10">
                            <button
                                onClick={goToPrevious}
                                className="p-2 rounded-full border border-white text-gray-500 hover:bg-gray-100"
                                aria-label="Previous testimonial">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round">
                                    <path d="M15 18l-6-6 6-6" />
                                </svg>
                            </button>

                            <button
                                onClick={goToNext}
                                className="p-2 rounded-full border border-white text-gray-500 hover:bg-gray-100"
                                aria-label="Next testimonial">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round">
                                    <path d="M9 18l6-6-6-6" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default TestimonialSlider
