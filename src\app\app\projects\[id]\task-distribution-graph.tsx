'use client'

import { TaskDistributionChart } from '@/components/chart/task-distribution'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { useAuthStore } from '@/store/auth.store'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'next/navigation'
import { toast } from 'sonner'

const TaskDistributionGraph = () => {
    const params = useParams()
    const projectId = params.id
    const { currentWorkspace } = useAuthStore()

    const { data: taskDistributionData, isLoading } = useQuery({
        queryKey: ['task-distribution', projectId, currentWorkspace?.id],
        queryFn: async () => {
            try {
                const response = await api.get(endpoints.project.getTaskDistribution, {
                    params: {
                        project_id: projectId,
                        workspace_id: currentWorkspace?.id,
                    },
                })
                return response.data.data
            } catch (error) {
                toast.error('Failed to fetch task distribution data')
                throw error
            }
        },
        enabled: !!projectId && !!currentWorkspace?.id,
    })

    return <TaskDistributionChart data={taskDistributionData} isDataLoading={isLoading || !currentWorkspace?.id} />
}

export default TaskDistributionGraph
