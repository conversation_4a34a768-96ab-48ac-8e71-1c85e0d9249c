'use client'

import dynamic from 'next/dynamic'
import { Suspense } from 'react'
import { Loader } from 'lucide-react'

const VerifyOtpContent = dynamic(() => import('./verify-otp-form'), {
    ssr: false,
})

export default function Page() {
    return (
        <Suspense
            fallback={
                <div className="flex justify-center items-center min-h-screen">
                    <Loader className="w-6 h-6 animate-spin" />
                </div>
            }>
            <VerifyOtpContent />
        </Suspense>
    )
}
