'use client'

import React, { useState, use<PERSON><PERSON>back, ChangeEvent } from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import TaskCard from '@/components/scratchpad-task-card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { CloudIcon, FigmaIcon, JavaScriptIcon, NodeIcon, PythonIcon, ReactIcon, TypeScriptIcon } from '@/icons/tech-icons'
import { MultiSelect } from '@/components/tech-stack-multiselect'
import { ClipboardList } from 'lucide-react'
import PromptTemplateModal from '@/components/prompt-template-modal'
import { useProjectStore } from '@/store/scratchpad-projects.store'
import endpoints from '@/services/api-endpoints'

interface Task {
    id: string
    category: string
    title: string
    description: string
    assignedRole: string
    estimatedHours: number
    dependencies: string[]
}

interface TaskResponse {
    task: Task
}

const options = [
    { id: 'figma', label: 'Figma', icon: <FigmaIcon className="h-4 w-4" /> },
    { id: 'azure', label: 'Azure', icon: <CloudIcon className="h-4 w-4 text-blue-600" /> },
    { id: 'python', label: 'Python', icon: <PythonIcon className="h-4 w-4" /> },
    { id: 'nextjs', label: 'NextJS', icon: <ReactIcon className="h-4 w-4 text-blue-400" /> },
    { id: 'typescript', label: 'TypeScript', icon: <TypeScriptIcon className="h-4 w-4 text-blue-600" /> },
    { id: 'javascript', label: 'JavaScript', icon: <JavaScriptIcon className="h-4 w-4 text-yellow-400" /> },
    { id: 'node', label: 'Node.js', icon: <NodeIcon className="h-4 w-4 text-green-600" /> },
    { id: 'postgres', label: 'Postgres', icon: <NodeIcon className="h-4 w-4 text-green-600" /> },
    { id: 'mongodb', label: 'MongoDB', icon: <NodeIcon className="h-4 w-4 text-green-600" /> },
]

function ScratchPadView() {
    const { selectedProject } = useProjectStore()
    const [expanded, setExpanded] = useState(false)
    const [feature, setFeature] = useState('User Authentication and Authorization')
    const [requirement, setRequirement] = useState(
        'The application should allow users to register, log in, and manage their accounts. It must support secure authentication (e.g., email/password, social media logins, or two-factor authentication). The system must differentiate between user roles (e.g., admin, regular user) and provide access control based on those roles.',
    )
    const [techStack, setTechStack] = useState<string[]>([])
    const [tasks, setTasks] = useState<Task[]>([])
    const [isModalOpen, setIsModalOpen] = useState(false)

    const extractTaskData = (input: string): TaskResponse | null => {
        const match = input.match(/data:\s*(\{.*\})/)
        if (match && match[1]) {
            try {
                return JSON.parse(match[1]) as TaskResponse
            } catch (error) {
                console.error('Invalid JSON format:', error)
                return null
            }
        }
        return null
    }

    const readStreamData = async (feature: string, requirement: string, techStack: string[]) => {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${endpoints.llm.interactiveAgent}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Accept: 'text/event-stream',
            },
            body: JSON.stringify({ message: `${feature} ${requirement} ${techStack.join(' ')}` }),
        })

        if (res.body) {
            const reader = res.body.getReader()
            const decoder = new TextDecoder()

            try {
                while (true) {
                    const { done, value } = await reader.read()
                    if (done) break

                    const chunk = decoder.decode(value)
                    const taskData = extractTaskData(chunk)

                    if (taskData?.task) {
                        setTasks((prevTasks) => [...prevTasks, taskData.task])
                    }
                }
            } finally {
                reader.releaseLock()
            }
        }
    }
    const handleSubmit = async () => {
        setTasks([])
        try {
            await readStreamData(feature, requirement, techStack)
        } catch (error) {
            console.error('Error fetching data:', error)
            alert('An error occurred while fetching tasks. Please try again.')
        }
    }

    const handleTitleChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        setFeature(e.target.value)
    }, [])

    const handleRequirementChange = useCallback((e: ChangeEvent<HTMLTextAreaElement>) => {
        setRequirement(e.target.value)
    }, [])

    return (
        <motion.div layout className="relative w-full bg-blue-500 flex flex-row gap-4 justify-between">
            {/* Left Panel - feature requirements */}
            <Button variant={'outline'} onClick={() => setIsModalOpen(true)} className="absolute right-0 top-[-120px] p-2">
                <ClipboardList className="w-[17px] h-[17px]" /> Prompt Templates
            </Button>
            <PromptTemplateModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
            <motion.div
                layout
                initial={{ opacity: 1 }}
                animate={{
                    display: !expanded ? 'flex' : 'none',
                    x: !expanded ? '0%' : '-150%',
                    opacity: !expanded ? 1 : 0,
                }}
                transition={{
                    duration: 0.4,
                }}
                className="absolute top-0 left-0 flex flex-col w-[580px] h-fit overflow-x-auto bg-[#F0F0F3] bg-[url('/assets/bg/scratchpad-req-bg.png')] bg-cover bg-center rounded-[12px] border border-white shadow-xl p-[30px]">
                <span className="mb-[5px] mt-[10px] text-[12px] font-semibold text-[#171A1F]">Project Title</span>
                <Input disabled value={selectedProject?.name} className="bg-white rounded-[10px] h-[37px]" />
                <span className="mb-[5px] mt-[10px] text-[12px] font-semibold text-[#171A1F]">Tech Stack</span>
                <div className="flex flex-row gap-2 items-center">
                    <MultiSelect
                        options={options}
                        selectedValues={techStack}
                        onChange={setTechStack}
                        placeholder="Select technologies"
                        maxDisplay={2}
                    />
                </div>
                <span className="mb-[5px] mt-[10px] text-[12px] font-semibold text-[#171A1F]">Feature Name*</span>
                <Input value={feature} onChange={handleTitleChange} className="bg-white rounded-[10px] h-[37px]" />
                <div className="flex flex-row items-end justify-between">
                    <span className="mb-[5px] mt-[10px] text-[12px] font-semibold text-[#171A1F]">Feature Requirements*</span>
                    <div className="cursor-pointer mb-[3px] gap-2 flex flex-row rounded-[6px] bg-[#1D1C1E03] px-[5px] py-[3px] border border-[#d7d7d7]">
                        <Image className="h-auto w-auto" src={'/assets/icons/stars.svg'} height={15} width={10} alt="AI Icon" />
                        <span className="text-[12px] text-[#2FA87A] font-semibold">Rewrite with AI</span>
                    </div>
                </div>
                <Textarea
                    value={requirement}
                    onChange={handleRequirementChange}
                    className="bg-white rounded-[10px] min-h-[176px]"
                />
                <Button onClick={handleSubmit} className="my-[20px] h-[43px]">
                    <Image src={'/assets/icons/wand.svg'} height={20} width={20} alt="Generate with AI wand" />
                    Generate
                </Button>
                <div className="flex flex-row gap-2 items-center text-[12px]">
                    <div className="font-bold text-[#160647]">10</div>
                    <div className="text-[#A5A1B1]">days remaining. Resets on Apr 15, 2023. Don’t worry!</div>
                    <div className="text-[#2FA87A] font-semibold">Upgrade now!</div>
                </div>
            </motion.div>

            {/* Right Panel - Task Listing */}
            <motion.div
                initial={{ left: 620, right: 0 }}
                animate={{ left: expanded ? 0 : 620 }}
                className="absolute top-0 flex flex-col rounded-[12px] border border-[#F0F0F0] pl-[0px] p-[15px]">
                <div className="ml-[15px] flex flex-row items-center justify-between">
                    <div className="flex flex-row items-center gap-2">
                        <div className="flex justify-center items-center rounded-full w-[25px] h-[25px] px-[6px] py-[7px] bg-[#8181821F]">
                            <Image src={'/assets/icons/tasks.svg'} width={14} height={11} alt="Tasks Icon" />
                        </div>
                        <h1 className="pl-[7px] text-[17px] font-semibold text-[#363636]">Total Tasks</h1>
                        <div className="h-[20px] w-[30px] flex justify-center items-center p-[5px] rounded-[40px] bg-[#dfdfdfde]">
                            <span className="text-[13px] font-semibold text-[#363636]">{tasks.length}</span>
                        </div>
                    </div>
                    <div className="cursor-pointer" onClick={() => setExpanded(!expanded)}>
                        <Image src={'/assets/icons/fullscreen.svg'} height={24} width={24} alt="Fullscreen Icon" />
                    </div>
                </div>
                <ScrollArea className="mt-[10px] h-[580px] p-4 overflow-visible" id="task-scroll-area">
                    {tasks.length ? (
                        tasks.map((task, i) => (
                            <TaskCard
                                key={task.id}
                                taskNum={i + 1}
                                id={task.id}
                                category={task.category}
                                title={task.title}
                                description={task.description}
                                assignedRole={task.assignedRole}
                                estimatedHours={task.estimatedHours}
                                dependencies={task.dependencies}
                            />
                        ))
                    ) : (
                        <p>Nothing to display. enter a requirement</p>
                    )}
                </ScrollArea>
            </motion.div>
        </motion.div>
    )
}

export default ScratchPadView
