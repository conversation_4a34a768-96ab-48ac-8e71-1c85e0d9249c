'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import { useFilterStore } from '@/store/filter-store'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Loader, Search } from 'lucide-react'
import type { FilterCategory, FilterOption } from './types'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { useAuthStore } from '@/store/auth.store'

interface FilterPanelProps {
    category: FilterCategory
    endpoint: string
    params?: Record<string, unknown>
}

export function FilterPanel({ category, endpoint, params }: FilterPanelProps) {
    const {
        searchQuery,
        setSearchQuery,
        tempSelectedFilters,
        toggleTempFilter,
        activeCategory,
        activeEndPoint,
        clearAllFilters,
        getTotalTempSelectedCount,
    } = useFilterStore()

    const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)
    const scrollRef = useRef<HTMLDivElement>(null)
    const [isNearBottom, setIsNearBottom] = useState(false)
    const { currentWorkspace } = useAuthStore()

    const DATA_FOR_ACTIVE_CATEGORY = {
        projects: {
            accessor: 'projectData',
            listLabel: 'name',
        },
        status: {
            accessor: 'data',
            listLabel: 'label',
        },
        department: {
            accessor: 'data',
            listLabel: 'label',
        },
        assignee: {
            accessor: 'data',
            listLabel: 'first_name',
        },
        feat_id: {
            accessor: 'data',
            listLabel: 'title',
        },
        bug: {
            accessor: 'data',
            listLabel: 'label',
        },
    }

    // Reset search query when category changes
    useEffect(() => {
        setLocalSearchQuery('')
        setSearchQuery('')
    }, [category?.id, setSearchQuery])

    // Fetch filter options
    const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, isError, error } = useInfiniteQuery({
        queryKey: ['filterOptions', category?.id, endpoint, searchQuery, currentWorkspace?.id],
        queryFn: async ({ pageParam = 1 }) => {
            try {
                if (activeCategory === 'bug') {
                    return {
                        data: [
                            { id: '1', label: 'Show only bugs' },
                            { id: '0', label: 'Hide bugs' },
                        ],
                        paginationData: {
                            total: 2,
                            currentPage: 1,
                            totalPages: 1,
                            pageSize: 2,
                        },
                    }
                }
                const response = await api.get(activeEndPoint || endpoint, {
                    params: {
                        ...params,
                        page: pageParam,
                        limit: 20,
                        searchQuery: searchQuery || undefined,
                        project_id: activeCategory === 'feat_id' ? tempSelectedFilters?.projects : undefined,
                        workspace_id: currentWorkspace?.id,
                    },
                })

                return response.data.data
            } catch (error) {
                toast.error('Error fetching projects. Please try again later.')
                throw error
            }
        },
        getNextPageParam: (lastPage) => {
            const { paginationData } = lastPage
            return paginationData?.currentPage < paginationData?.totalPages ? paginationData?.currentPage + 1 : undefined
        },
        initialPageParam: 1,
        retry: false,
        enabled: activeCategory !== 'bug' && !!currentWorkspace?.id,
    })

    // Handle scroll to load more
    const handleScroll = useCallback(() => {
        if (!scrollRef.current) return

        const { scrollTop, scrollHeight, clientHeight } = scrollRef.current
        const scrollBottom = scrollHeight - scrollTop - clientHeight
        const isNearBottom = scrollBottom < 100

        setIsNearBottom(isNearBottom)
    }, [])

    // Load more when near bottom
    useEffect(() => {
        if (isNearBottom && hasNextPage && !isFetchingNextPage) {
            fetchNextPage()
        }
    }, [isNearBottom, hasNextPage, isFetchingNextPage, fetchNextPage])

    // Get all options from all pages
    const isContainArray = data?.pages[0]?.length
    const accessor = DATA_FOR_ACTIVE_CATEGORY[activeCategory as keyof typeof DATA_FOR_ACTIVE_CATEGORY]?.accessor
    const allOptions = isContainArray
        ? data?.pages.flatMap((page) => page) || []
        : data?.pages?.flatMap((page) => page?.[accessor]) || []
    const listLabel = DATA_FOR_ACTIVE_CATEGORY[activeCategory as keyof typeof DATA_FOR_ACTIVE_CATEGORY]?.listLabel
    // Filter options by search query

    let filteredOptions = allOptions
    if (activeCategory === 'bug') {
        filteredOptions = [
            { id: '1', label: 'Show only bugs' },
            { id: '0', label: 'Hide bugs' },
        ]
    }

    // Get selected options for this category
    const selectedOptions = tempSelectedFilters[category?.id] || []

    // Handle search input change with debounce
    useEffect(() => {
        const timer = setTimeout(() => {
            setSearchQuery(localSearchQuery)
        }, 300)

        return () => clearTimeout(timer)
    }, [localSearchQuery, setSearchQuery])

    const selectedCount = getTotalTempSelectedCount()

    return (
        <div className="flex h-full flex-col">
            <div className=" px-4 pt-4">
                <div className="flex w-full justify-between items-center px-2 text-sm text-[#9BA5B1]">
                    {selectedCount && selectedCount} Selected
                    {selectedCount > 0 && (
                        <Button variant="ghost" className="h-auto p-0 text-sm text-[#1C7E84]" onClick={() => clearAllFilters()}>
                            Clear all
                        </Button>
                    )}
                </div>
                {activeCategory !== 'bug' && (
                    <div className="flex items-center gap-2 mt-2">
                        <div className="relative flex-1 ">
                            <Input
                                type="text"
                                placeholder="Search options..."
                                className="pl-2 text-sm"
                                value={localSearchQuery}
                                onChange={(e) => setLocalSearchQuery(e.target.value)}
                            />
                            <Search className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        </div>
                    </div>
                )}
            </div>

            <div className="p-4">
                {isLoading ? (
                    <div className="space-y-2 flex mx-auto justify-center items-center w-full h-[200px]">
                        <Loader size={15} className="animate-spin" />
                    </div>
                ) : isError ? (
                    <div className="flex h-full flex-col items-center justify-center p-4 text-center">
                        <p className="text-sm text-destructive">Error loading options: {error.message}</p>
                        <Button variant="outline" size="sm" className="mt-2" onClick={() => window.location.reload()}>
                            Retry
                        </Button>
                    </div>
                ) : filteredOptions.length === 0 || filteredOptions[0] === undefined ? (
                    <div className="flex h-full flex-col items-center justify-center p-4 text-center">
                        <p className="text-sm text-muted-foreground">No options found</p>
                        {localSearchQuery && (
                            <Button variant="outline" size="sm" className="mt-2" onClick={() => setLocalSearchQuery('')}>
                                Clear search
                            </Button>
                        )}
                    </div>
                ) : (
                    <div className="space-y-1">
                        <ScrollArea ref={scrollRef} onScroll={handleScroll} className="flex-1 h-55 ">
                            {filteredOptions.map((option: FilterOption) => {
                                const isChecked = selectedOptions.includes(option?.id)
                                return (
                                    <div
                                        key={option?.id}
                                        className="flex items-center gap-2 hover:bg-[#0000000A] p-2 rounded-sm mb-1"
                                        style={{ background: isChecked ? '#0000000A' : '' }}>
                                        <Checkbox
                                            id={`option-${option?.id}`}
                                            checked={isChecked}
                                            style={{
                                                background: isChecked ? '#1C7E84' : '',
                                                border: !isChecked ? '1px solid #000' : 'none',
                                            }}
                                            onCheckedChange={() =>
                                                toggleTempFilter(category?.id, option?.id, activeCategory === 'bug')
                                            }
                                        />
                                        <label htmlFor={`option-${option?.id}`} className="flex-1 cursor-pointer text-sm">
                                            {(option?.[listLabel] as string) || (option?.name as string)}
                                        </label>
                                    </div>
                                )
                            })}
                        </ScrollArea>
                        {isFetchingNextPage && (
                            <div className="space-y-2 flex mx-auto justify-center items-center w-full h-[200px]">
                                <Loader size={15} className="animate-spin" />
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    )
}
