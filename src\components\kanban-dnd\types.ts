export interface DnDColumnType {
    id: number
    short_code: string
    label: string
    kanban_column: boolean
    column_order: number
    colour: string
    createdAt: string
    updatedAt: string
    deletedAt: string | null
}

type DependentTaskType = {
    id: number
    short_code: string
    title: string
    status_id: number
    taskStatus: {
        id: number
        short_code: string
        label: string
    }
    task_dependency_mapping: {
        dependency_level: number
    }
    taskDepartment?: TaskDepartmentType
}

type DependentTasks = DependentTaskType[]
type TaskDepartmentType = {
    id: number
    short_code: string
}

export interface DnDTaskType {
    id: number
    feat_id: number
    short_code: string
    title: string
    description: string
    time_estimate_hrs: number
    start_date: string
    due_date: string
    status_id: number
    assignee: number
    created_by: number
    createdAt: string
    updatedAt: string
    deletedAt: string | null
    department: number | null
    feature: {
        id: number
        feat_code: string
        title: string
    }
    taskStatus: {
        id: number
        short_code: string
        label: string
        colour: string
    }
    assignedUser: {
        id: number
        first_name: string
        last_name: string
    }
    creator: {
        id: number
        first_name: string
        last_name: string
    }
    taskDepartment: TaskDepartmentType
    priority?: string
    column?: number
    dueDate?: string
    taskPriority?: {
        id?: number
        label: string
        color?: string
    }
    dependentTasks?: DependentTasks
    bug?: boolean
    taskAssignees?:
        | [
              {
                  id: number
                  first_name: string
                  last_name: string
                  img_url?: string
              },
          ]
        | []
}

export interface TaskUpdateApiBodyType {
    id: number
    feat_id: number
    short_code: string
    title: string
    description: string
    time_estimate_hrs: number
    start_date: string // ISO string or 'YYYY-MM-DD' format
    due_date: string // ISO string or 'YYYY-MM-DD' format
    status: number // or number, depending on your system
    assignee: number // or number, if using user ID
    department: number | null
    movedToIndex: number
    assignedUser: {
        id: number
    }
}
