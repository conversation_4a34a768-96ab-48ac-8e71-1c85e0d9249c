import { useState, useRef, useEffect, KeyboardEvent } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { SendIcon, PlusIcon, ClipboardIcon } from 'lucide-react'
import { motion } from 'framer-motion'
import { ViewToggle } from '../../app/app/chat/view-toggle'

interface ChatInputProps {
    onSend: (message: string) => void
    isLoading?: boolean
    onViewToggle?: (view: 'chat' | 'project') => void
    currentView?: 'chat' | 'project'
}

export function ChatInput({ onSend, isLoading, onViewToggle, currentView = 'chat' }: ChatInputProps) {
    const [message, setMessage] = useState('')
    const textareaRef = useRef<HTMLTextAreaElement>(null)

    // Auto-resize textarea based on content
    useEffect(() => {
        if (textareaRef.current) {
            // Reset height to calculate the right height
            textareaRef.current.style.height = 'auto'

            // Set new height based on scrollHeight, with a max of 120px
            const newHeight = Math.min(textareaRef.current.scrollHeight, 120)
            textareaRef.current.style.height = `${newHeight}px`
        }
    }, [message])

    const handleSend = () => {
        if (message.trim() && !isLoading) {
            onSend(message.trim())
            setMessage('')

            // Reset textarea height
            if (textareaRef.current) {
                textareaRef.current.style.height = 'auto'
            }
        }
    }

    const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            handleSend()
        }
    }

    return (
        <div className="bg-white p-4 rounded-xl shadow-lg">
            <div className="relative mb-2 border-b border-gray-100 dark:border-gray-800">
                <Textarea
                    ref={textareaRef}
                    className="w-full text-md text-gray-600 resize-none dark:text-gray-300 bg-transparent border-none focus-visible:ring-0 focus-visible:ring-offset-0 pl-0 min-h-[60px] max-h-[120px] overflow-y-auto"
                    placeholder="How can I help you?"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={handleKeyPress}
                    disabled={isLoading}
                    rows={1} // Start with one row
                />
            </div>

            <div className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-4">
                    {/* View Toggle positioned at bottom left */}
                    {onViewToggle && (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.1 }}
                            className="flex-shrink-0">
                            <ViewToggle currentView={currentView} onToggle={onViewToggle} />
                        </motion.div>
                    )}

                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                        <ClipboardIcon className="h-4 w-4" />
                        <span className="text-sm">New Chat</span>
                        <PlusIcon className="h-4 w-4" />
                    </Button>

                    <Button variant="outline" size="sm" className="p-2">
                        <ClipboardIcon className="h-4 w-4" />
                    </Button>
                </div>

                <Button
                    size="icon"
                    className="px-7 bg-gray-950 dark:bg-gray-700 hover:bg-gray-800 dark:hover:bg-gray-600 text-white rounded-md"
                    onClick={handleSend}
                    disabled={isLoading || !message.trim()}>
                    <SendIcon />
                </Button>
            </div>
        </div>
    )
}
