'use client'

import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Puzzle, MessageSquare, FileText } from 'lucide-react'
import AttachmentsTab from './attachments'
import FeaturesTab from './features'
import CommentsTab from './comments'
import { Button } from '@/components/ui/button'
import { useTabStore } from '@/store/tabs.store'
import CreateFeatureModal from './modals/create-feature-modal'
import { useParams } from 'next/navigation'
import { useQueryClient } from '@tanstack/react-query'

interface TabItem {
    id: string
    label: string
    icon?: React.ReactNode
    content: React.ReactNode
    count?: number
}

export default function ProjectBottomTabs() {
    const { projectActiveTab, setProjectActiveTab } = useTabStore()
    const params = useParams()
    const projectId = Number(params.id)
    const queryClient = useQueryClient()
    const onSuccess = () => {
        queryClient.invalidateQueries({ queryKey: ['features'] })
    }

    // Create an array of tab items with optional count badges
    const tabItems: TabItem[] = [
        {
            id: 'features',
            label: 'Features',
            content: <FeaturesTab />,
            count: 12,
            icon: <Puzzle className="h-4 w-4" />,
        },
        {
            id: 'attachments',
            label: 'Attachments',
            icon: <FileText className="h-4 w-4" />,
            content: <AttachmentsTab />,
            count: 5,
        },
        {
            id: 'comments',
            label: 'Comments',
            icon: <MessageSquare className="h-4 w-4" />,
            content: <CommentsTab />,
            count: 8,
        },
    ]

    return (
        <div className="flex mt-8 rounded-lg bg-transparent p-4 items-center">
            <Tabs value={projectActiveTab} onValueChange={setProjectActiveTab} className="w-full">
                <div className="flex flex-row justify-between items-center w-full">
                    <TabsList className="grid grid-cols-3 h-full bg-transparent">
                        {tabItems.map((tab) => (
                            <TabsTrigger
                                key={tab.id}
                                value={tab.id}
                                className="cursor-pointer flex items-center gap-2 py-2 data-[state=inactive]:text-[#575757] data-[state=active]:bg-[#2C2E2F0D] data-[state=active]:shadow-none">
                                {tab?.icon}
                                <span>{tab.label}</span>
                            </TabsTrigger>
                        ))}
                    </TabsList>
                    <CreateFeatureModal projectId={projectId} onSuccess={onSuccess}>
                        <Button>Add Feature</Button>
                    </CreateFeatureModal>
                </div>
                {tabItems.map((tab) => (
                    <TabsContent key={tab.id} value={tab.id} className="px-2">
                        {tab.content}
                    </TabsContent>
                ))}
            </Tabs>
        </div>
    )
}
