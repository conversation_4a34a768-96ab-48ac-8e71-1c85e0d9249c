'use client'

import { useState } from 'react'
import { CirclePlus } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { CustomModal, CustomModalTrigger } from '../../../../components/custom-modal'
import { CreateTaskForm, type TaskFormValues } from './create-task-form'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'

export function CreateTaskModal({
    modalTrigger,
    defaultValues,
}: {
    modalTrigger?: React.ReactNode
    defaultValues?: { statusName: string; priorityName: string }
}) {
    const [isOpen, setIsOpen] = useState(false)
    const queryClient = useQueryClient()

    const createTask = async (data: TaskFormValues) => {
        const body = {
            feat_id: Number(data.feature),
            title: data.taskName,
            description: data.description,
            due_date: data.dueDate,
            status: Number(data.status),
            assignee: data.teamMembers,
            department: Number(data.department),
            bug: data.isBug,
            short_code: `TASK${Math.floor(Math.random() * 1000)}`,
            priority_id: Number(data.priority),
            parent_task_short_code: data.parentTaskId,
        }
        const response = await api.post(endpoints.tasks.createTask, body)
        return response.data
    }

    const createTaskMutation = useMutation({
        mutationFn: createTask,
        onSuccess: () => {
            toast.success('Task created successfully')
            setIsOpen(false)
            // Invalidate and refetch tasks list
            queryClient.invalidateQueries({ queryKey: ['tasks'] })
        },
        onError: (error) => {
            toast.error('Failed to create task: ' + (error as Error).message)
        },
    })
    // Handle form submission
    function onSubmit(data: TaskFormValues) {
        createTaskMutation.mutate(data)
    }

    const openModal = () => setIsOpen(true)
    const closeModal = () => setIsOpen(false)

    return (
        <>
            <CustomModalTrigger onClick={openModal} className="h-full">
                {modalTrigger ? (
                    modalTrigger
                ) : (
                    <Button variant="outline" className="bg-[#000] hover:bg-[#060606] h-full">
                        <div className="flex items-center justify-center text-white gap-2">
                            <CirclePlus color="#fff" />
                            <p>Add Task</p>
                        </div>
                    </Button>
                )}
            </CustomModalTrigger>

            <CustomModal isOpen={isOpen} onClose={closeModal} className="sm:max-w-[723px] max-h-[98vh]  overflow-auto">
                <div className="p-6">
                    <CreateTaskForm onSubmit={onSubmit} defaultValues={defaultValues} />
                </div>
            </CustomModal>
        </>
    )
}
