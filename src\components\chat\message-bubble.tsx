import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, EllipsisVertical, RotateCw, ExpandIcon, MinimizeIcon, User } from 'lucide-react'
import { Message } from '@/types/chat'
import Markdown from 'react-markdown'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import Image from 'next/image'

interface MessageBubbleProps {
    message: Message
}

export function MessageBubble({ message }: MessageBubbleProps) {
    const isUser = message.sender === 'user'
    const [cleanedMessage, setCleanedMessage] = useState<string>('')
    const [isExpanded, setIsExpanded] = useState(true)

    // Process message content
    useEffect(() => {
        if (typeof message.content === 'string') {
            setCleanedMessage(message.content)
        } else {
            setCleanedMessage(String(message.content))
        }
    }, [message])

    // Handle copying text to clipboard
    const handleCopy = (text: string) => {
        navigator.clipboard.writeText(text)
        toast.success('Copied to clipboard!')
    }

    // Check if message is long enough to warrant expansion controls
    const isLongMessage = cleanedMessage.length > 500

    return (
        <div className={`mb-4 flex ${isUser ? 'justify-end' : 'justify-start'}`}>
            <div
                className={`rounded-2xl px-4 py-3 ${isUser
                        ? 'bg-[#F7F7F7] text-[#666F8D] shadow-sm border border-[#F0F2F5] max-w-[80%]'
                        : isLongMessage
                            ? 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 w-full max-w-[95%]'
                            : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 max-w-[80%]'
                    }`}>
                {isUser ? (
                    <div className="flex flex-row gap-4 w-full">
                        <div className="flex justify-center items-center h-[24px] w-[24px] p-[3px] bg-[#F6FAFF] rounded-full">
                            <User className="text-[#2388FF]" />
                        </div>
                        <div className="flex flex-col">
                            <p className="text-md">{cleanedMessage}</p>
                            <div className="border-t border-gray-200 dark:border-gray-700 pt-3 pb-0 flex flex-row justify-between mt-2">
                                <div className="flex flex-row justify-between items-center gap-2">
                                    <button className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700">
                                        <RotateCw size={14} />
                                    </button>
                                    <button
                                        className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
                                        onClick={() => handleCopy(cleanedMessage)}>
                                        <Copy size={14} />
                                    </button>
                                    <button className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700">
                                        <EllipsisVertical size={14} />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="w-full">
                        <div className="flex flex-row gap-4">
                            <div className="flex justify-center items-center h-[24px] min-w-[24px] p-[3px] bg-[#F6FAFF] rounded-full">
                                <Image src={'/assets/img/raydian-logo.png'} width={21} height={21} alt="Scratchpad bot" />
                            </div>
                            <div className="flex flex-col">
                                <div
                                    className={`message-content ${!isExpanded && isLongMessage ? 'max-h-32 overflow-hidden relative' : ''
                                        }`}>
                                    <Markdown
                                        components={{
                                            h1: ({ ...props }) => <h1 className="text-2xl font-bold my-4" {...props} />,
                                            h2: ({ ...props }) => <h2 className="text-xl font-semibold my-3" {...props} />,
                                            h3: ({ ...props }) => <h3 className="text-lg font-medium my-2" {...props} />,
                                            p: ({ ...props }) => <p className="text-md leading-relaxed mb-2" {...props} />,
                                            ul: ({ ...props }) => <ul className="list-disc pl-6 mb-2" {...props} />,
                                            ol: ({ ...props }) => <ol className="list-decimal pl-6 mb-2" {...props} />,
                                            li: ({ ...props }) => <li className="mb-1" {...props} />,
                                            a: ({ ...props }) => <a className="text-blue-500 hover:underline" {...props} />,
                                            code: ({ ...props }) => (
                                                <code
                                                    className="inline-flex w-fit bg-gray-200 dark:bg-gray-700 p-0 rounded-xl text-xs text-amber-800 my-1 whitespace-pre-wrap"
                                                    {...props}
                                                />
                                            ),
                                            pre: ({ children, ...props }) => {
                                                // Extract plain text from children
                                                const codeContent = Array.isArray(children)
                                                    ? children.map((child) => (typeof child === 'string' ? child : '')).join('')
                                                    : String(children).trim()

                                                return (
                                                    <div className="relative group">
                                                        <pre
                                                            className="bg-gray-200 dark:bg-gray-700 text-xs p-2 rounded my-2 overflow-x-auto"
                                                            {...props}>
                                                            {children}
                                                        </pre>
                                                        <button
                                                            onClick={() => handleCopy(codeContent)}
                                                            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-gray-300 dark:bg-gray-600 p-1 rounded text-xs">
                                                            <Copy size={14} />
                                                        </button>
                                                    </div>
                                                )
                                            },
                                            blockquote: ({ ...props }) => (
                                                <blockquote className="border-l-4 border-gray-300 pl-4 italic my-2" {...props} />
                                            ),
                                        }}>
                                        {isLongMessage && !isExpanded ? cleanedMessage.substring(0, 200) + '...' : cleanedMessage}
                                    </Markdown>
                                </div>

                                {/* Show fade effect and expand button for long messages */}
                                {isLongMessage && (
                                    <div className={`w-full flex justify-center mt-2 ${!isExpanded ? 'relative' : ''}`}>
                                        {!isExpanded && (
                                            <div className="absolute bottom-8 left-0 w-full h-12 bg-gradient-to-t from-gray-100 dark:from-gray-800 to-transparent"></div>
                                        )}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setIsExpanded(!isExpanded)}
                                            className="text-xs flex items-center gap-1">
                                            {isExpanded ? (
                                                <>
                                                    <MinimizeIcon className="h-3 w-3" />
                                                    Show Less
                                                </>
                                            ) : (
                                                <>
                                                    <ExpandIcon className="h-3 w-3" />
                                                    Show More
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                )}

                                {/* Action buttons */}
                                <div className="border-t border-gray-200 dark:border-gray-700 pt-3 pb-0 flex flex-row justify-between mt-2">
                                    <div className="flex flex-row justify-between items-center gap-2">
                                        <button className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700">
                                            <RotateCw size={14} />
                                        </button>
                                        <button
                                            className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
                                            onClick={() => handleCopy(cleanedMessage)}>
                                            <Copy size={14} />
                                        </button>
                                        <button className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700">
                                            <EllipsisVertical size={14} />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}
