'use client'

import type { FC } from 'react'
import { Code, Cloud, Search, Pencil } from 'lucide-react'

// Props for the Department component
interface DepartmentProps {
    id?: number
    label?: string
    shortCode: string
    size?: 'sm' | 'md' | 'lg'
}

/**
 * Renders the appropriate icon based on department ID
 */
const getDepartmentIcon = (departmentId: string) => {
    const iconClassName = ''

    switch (departmentId.toLowerCase()) {
        case 'design':
            return <Pencil size={12} className={iconClassName} />
        case 'development':
            return <Code size={12} className={iconClassName} />
        case 'qa':
            return <Search size={12} className={iconClassName} />
        case 'devops':
            return <Cloud size={12} className={iconClassName} />
        default:
            // No icon for unknown departments
            return null
    }
}

/**
 * Returns the appropriate className based on department ID
 */
const getDepartmentClassName = (shortCode: string): string => {
    switch (shortCode) {
        case 'design':
            return 'bg-[#007BFF0D] text-[#007BFF] border-[#007BFF1F]'
        case 'development':
            return 'bg-[#28A74514] text-[#28A745] border-[#28A7451F]'
        case 'qa':
            return 'bg-[#F4EDF7] text-[#6E65AA] border-[#6E65AA1F]'
        case 'devops':
            return 'bg-[#FED7AA1F] text-[#F97316] border-[#FED7AA1F]'
        default:
            return 'bg-gray-100 text-gray-500 border-gray-200'
    }
}

/**
 * Department component that renders a chip based on the provided departmentId
 */
export const DepartmentChip: FC<DepartmentProps> = ({ shortCode, label, size = 'md' }) => {
    // Get department data or use a fallback for unknown departments
    const department = {
        id: shortCode,
        name: label,
    }

    // Get the appropriate icon based on department ID
    const icon = getDepartmentIcon(shortCode)

    // Get the appropriate className based on department ID
    const className = getDepartmentClassName(shortCode)

    // Size classes mapping
    const sizeClasses = {
        sm: 'text-xs px-2 py-[3px]',
        md: 'text-sm px-3 py-1',
        lg: 'text-base px-4 py-2',
    }

    return (
        <div
            className={`inline-flex items-center gap-1 mx-[2px] rounded-md border ${className} ${sizeClasses[size]}`}
            data-department-id={department.id}>
            {icon}
            <span>{department.name}</span>
        </div>
    )
}

// Default export for easier imports
export default DepartmentChip
