'use client'

import React from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'

export default function HomePage() {
    return (
        <div className="min-h-screen">
            {/* Hero Section */}
            <section className="h-screen flex flex-col items-center justify-center px-4 relative overflow-hidden">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    className="text-center z-10">
                    <motion.h1
                        className="text-5xl md:text-7xl font-bold mb-6"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.3, duration: 0.8 }}>
                        Welcome to Radian
                    </motion.h1>
                    <motion.p
                        className="text-xl md:text-2xl text-gray-600 max-w-2xl mx-auto"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.6, duration: 0.8 }}>
                        Streamline your workflow with our powerful platform
                    </motion.p>
                </motion.div>

                <motion.div
                    className="absolute w-full max-w-4xl"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 0.8, scale: 1 }}
                    transition={{ delay: 0.9, duration: 1 }}>
                    <Image
                        src="/assets/img/raydian-logo.png"
                        alt="Radian Logo"
                        width={600}
                        height={400}
                        className="mx-auto opacity-20"
                    />
                </motion.div>
            </section>

            {/* Features Section */}
            <section className="py-20 bg-gray-50">
                <div className="max-w-6xl mx-auto px-4">
                    <motion.h2
                        className="text-3xl md:text-4xl font-bold text-center mb-16"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.6 }}>
                        Key Features
                    </motion.h2>

                    <div className="grid md:grid-cols-3 gap-8">
                        {[
                            {
                                title: 'Intuitive Design',
                                description: 'User-friendly interface that makes complex tasks simple.',
                                icon: '🎨',
                            },
                            {
                                title: 'Powerful Analytics',
                                description: 'Gain insights with comprehensive data visualization.',
                                icon: '📊',
                            },
                            {
                                title: 'Seamless Integration',
                                description: 'Works with your existing tools and workflows.',
                                icon: '🔄',
                            },
                        ].map((feature, index) => (
                            <motion.div
                                key={index}
                                className="bg-white p-8 rounded-xl shadow-sm"
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ delay: index * 0.2, duration: 0.5 }}
                                whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}>
                                <div className="text-4xl mb-4">{feature.icon}</div>
                                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                                <p className="text-gray-600">{feature.description}</p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Image Gallery Section */}
            <section className="py-20">
                <div className="max-w-6xl mx-auto px-4">
                    <motion.h2
                        className="text-3xl md:text-4xl font-bold text-center mb-16"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.6 }}>
                        Our Platform
                    </motion.h2>

                    <div className="grid md:grid-cols-2 gap-8">
                        {[1, 2, 3, 4].map((num, index) => (
                            <motion.div
                                key={index}
                                className="overflow-hidden rounded-xl shadow-sm"
                                initial={{ opacity: 0, scale: 0.9 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                viewport={{ once: true }}
                                transition={{ delay: index * 0.2, duration: 0.5 }}
                                whileHover={{ scale: 1.03 }}>
                                <div className="bg-gray-200 h-64 flex items-center justify-center">
                                    <div className="text-4xl text-gray-400">Image Placeholder {num}</div>
                                </div>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Call to Action */}
            <section className="py-20 bg-gray-900 text-white">
                <div className="max-w-4xl mx-auto px-4 text-center">
                    <motion.h2
                        className="text-3xl md:text-4xl font-bold mb-6"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.6 }}>
                        Ready to get started?
                    </motion.h2>

                    <motion.p
                        className="text-xl text-gray-300 mb-10"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        viewport={{ once: true }}
                        transition={{ delay: 0.2, duration: 0.6 }}>
                        Join thousands of teams that use Radian to improve their workflow
                    </motion.p>

                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ delay: 0.4, duration: 0.6 }}>
                        <button className="bg-white text-gray-900 px-8 py-3 rounded-full text-lg font-medium hover:bg-gray-100 transition-colors">
                            Get Started
                        </button>
                    </motion.div>
                </div>
            </section>
        </div>
    )
}
