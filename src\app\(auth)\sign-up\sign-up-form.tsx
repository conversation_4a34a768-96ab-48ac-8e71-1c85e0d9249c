'use client'

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Form, FormField } from '@/components/ui/form'

import GoogleLogo from '../../../../public/assets/img/GoogleLogo.svg' // fix these imports
import Ms<PERSON>ogo from '../../../../public/assets/img/MsLogo.svg' // fix these imports
import { Loader } from 'lucide-react'
import endpoints from '@/services/api-endpoints'
import { toast } from 'sonner'
import { api } from '@/config/axios-config'
import { useRouter, useSearchParams } from 'next/navigation'
import FormOverlayWrapper from '@/components/form-overlay-wrapper'
import { FormInputField, PasswordInputField } from '@/components/form-fields'
import { useAuthStore } from '@/store/auth.store'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'

// Extend the schema to include confirmPassword validation
const formSchema = z.object({
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    email: z.string().email('Invalid email'),
    password: z
        .string()
        .min(8, 'Password must be at least 8 characters long')
        .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
        .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
        .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
})

const SignUpForm = () => {
    const router = useRouter()
    const searchParams = useSearchParams()
    const redirectUrl = searchParams.get('redirect')
    const setPendingVerificationEmail = useAuthStore((state) => state.setPendingVerificationEmail)
    // React Hook Form setup
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: '',
            password: '',
            firstName: '',
            lastName: '',
        },
    })

    // React Query mutation for form submission
    const mutation = useMutation({
        mutationFn: async (values: z.infer<typeof formSchema>) => {
            const body = { userType: 1, confirmPassword: values.password, ...values }
            const response = await api.post(endpoints.authentication.signUp, body)
            return response.data
        },
        onSuccess: (data) => {
            toast.success('Verification email sent successfully.')
            setPendingVerificationEmail(data.email)
            if (redirectUrl) {
                router.push(`/verify-otp?redirect=${redirectUrl}`)
                return
            }
            router.push('/verify-otp')
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to send verification email. Please try again.')
        },
    })

    const onSubmit = (values: z.infer<typeof formSchema>) => {
        const toastId = toast.loading('Sending otp...') // Show loading toast
        mutation.mutate(values, {
            onSettled: () => {
                toast.dismiss(toastId) // Dismiss the loading toast when the mutation is settled
            },
        })
    }

    return (
        <div>
            <div className="flex mx-auto w-[380px]">
                <FormOverlayWrapper isSubmitting={mutation.isPending}>
                    <Card className="w-full max-w-xl border-none shadow-none mt-[80px]">
                        <CardHeader>
                            <CardTitle className="text-2xl py-1">Let&apos;s get started..</CardTitle>
                        </CardHeader>

                        <CardContent>
                            <Form {...form}>
                                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                                    <div className="flex w-full space-x-4">
                                        <div className="h-18 w-full">
                                            <FormField
                                                control={form.control}
                                                name="firstName"
                                                render={({ field }) => (
                                                    <FormInputField label="First Name *" placeholder="First name" field={field} />
                                                )}
                                            />
                                        </div>
                                        <div className="h-18 w-full">
                                            <FormField
                                                control={form.control}
                                                name="lastName"
                                                render={({ field }) => (
                                                    <FormInputField label="Last Name *" placeholder="Last name" field={field} />
                                                )}
                                            />
                                        </div>
                                    </div>
                                    <FormField
                                        control={form.control}
                                        name="email"
                                        render={({ field }) => (
                                            <FormInputField label="Email *" placeholder="Your email" type="email" field={field} />
                                        )}
                                    />

                                    {/* Password Field */}

                                    <FormField
                                        control={form.control}
                                        name="password"
                                        render={({ field }) => (
                                            <PasswordInputField label="Password *" placeholder="Your password" field={field} />
                                        )}
                                    />

                                    <Button type="submit" className="w-full rounded-sm mt-2" disabled={mutation.isPending}>
                                        {mutation.isPending ? <Loader className="animate-spin" /> : 'Create an account'}
                                    </Button>
                                </form>
                            </Form>

                            {/* OR separator and social logins */}
                            <div className="text-center space-y-4 mt-6">
                                <div className="my-4 flex items-center">
                                    <Separator style={{ width: '45%', backgroundColor: '#E9EAEB' }} />
                                    <span className="mx-2 text-xs text-gray-500">OR</span>
                                    <Separator style={{ width: '45%', backgroundColor: '#E9EAEB' }} />
                                </div>

                                <Button variant="outline" className="w-full bg-[#fff] rounded-sm">
                                    {' '}
                                    <div className="flex items-center justify-center w-full">
                                        <Image src={GoogleLogo} alt="Google Logo" width={20} height={20} className="mr-2" />
                                        Continue with Google
                                    </div>
                                </Button>

                                <Button variant="outline" className="w-full bg-[#fff] rounded-sm">
                                    <div className="flex items-center justify-center w-full pl-2">
                                        <Image src={MsLogo} alt="Microsoft Logo" width={20} height={20} className="mr-2" />
                                        Continue with Microsoft
                                    </div>
                                </Button>

                                <div className="text-[14px] tex-[#0B0B0B]">
                                    Already have an account?{' '}
                                    <Link
                                        href={redirectUrl ? `/sign-in?redirect=${redirectUrl}` : '/sign-in'}
                                        className="underline">
                                        Log In
                                    </Link>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </FormOverlayWrapper>
            </div>
        </div>
    )
}

export default SignUpForm
