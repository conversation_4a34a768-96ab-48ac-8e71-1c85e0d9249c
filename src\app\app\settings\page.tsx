'use client'

import { useEffect, useState } from 'react'
import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { useAuthStore } from '@/store/auth.store'
import { Loader } from 'lucide-react'
import SettingsTabs from './settings-components/settings-tabs'
import { uploadFile } from '@/lib/minio-client'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { toast } from 'sonner'
import endpoints from '@/services/api-endpoints'
import { useMutation } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import UserProfileHeader from '@/components/user-profile-header'

type FileUploadResult = {
    success: boolean
    fileUrl?: string | null
}

const Page = () => {
    const { setBreadcrumbs } = useBreadcrumbStore()
    const { user } = useAuthStore()
    const userId = useAuthStore((state) => state.user?.id)
    const refetchUserDetails = useAuthStore((state) => state.fetchMyDetails)
    const [isUploading, setIsUploading] = useState(false)

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/' },
            { label: 'Settings', href: '/app/settings' },
        ])
    }, [setBreadcrumbs])

    const mutation = useMutation({
        mutationFn: async (data: string) => {
            const payload = { coverImageUrl: data }
            const url = endpoints.user.updateUserById.replace(':id', userId?.toString() || '')
            const response = await api.patch(url, payload)
            return response.data
        },
        onSuccess: () => {
            toast.success('Cover Image updated successfully!')
            refetchUserDetails()
            setIsUploading(false)
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to update profile. Please try again.')
            setIsUploading(false)
        },
    })

    const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setIsUploading(true)
            let result: FileUploadResult = { success: false, fileUrl: '' }

            const formData = new FormData()
            formData.append('file', e.target.files[0])
            result = await uploadFile(formData)

            if (!result.success) {
                throw new Error('Upload failed')
            }

            if (result.fileUrl) {
                mutation.mutate(result.fileUrl)
            }
        }
    }

    if (!user) {
        return (
            <div className="flex items-center justify-center h-[88dvh]">
                <Loader className="animate-spin" />
            </div>
        )
    }
    return (
        <div className="space-y-40">
            <UserProfileHeader user={user} isUploading={isUploading} handleImageUpload={handleImageUpload} />
            <div>
                <SettingsTabs />
            </div>
        </div>
    )
}

export default Page
