import { useCallback, useMemo } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { toast } from 'sonner'
import { useProjectStore } from '@/store/project.store'
import { useAuthStore } from '@/store/auth.store'
import { ChatService } from '@/services/chat.service'
import { saveProject } from '@/services/project.service'
import { Message } from '@/types/project'

export const useProjectPlanner = () => {
    const chatService = useMemo(() => new ChatService(), [])

    const projectStore = useProjectStore()
    const {
        messages,
        tasks,
        projectData,
        projectSession,
        showTasksPanel,
        isLoading,
        isSaving,
        addMessage,
        updateMessage,
        setTasks,
        addTask,
        setProjectSession,
        setShowTasksPanel,
        setIsLoading,
        setIsSaving,
        updateProjectData,
        resetProject,
    } = projectStore

    const selectedProject = 'selectedProject' in projectStore ? projectStore.selectedProject : null
    const setSelectedProject =
        'setSelectedProject' in projectStore
            ? projectStore.setSelectedProject
            : (project: any) => {
                  console.log('setSelectedProject not available in store, selected:', project)
              }

    const { getUserId, getActiveWorkspaceId, user } = useAuthStore()

    const sendMessage = useCallback(
        async (message: string) => {
            if (!message.trim() || isLoading) return

            // Create or use existing session
            const sessionId = projectSession || uuidv4()
            if (!projectSession) {
                setProjectSession(sessionId)
            }

            // Add user message
            const userMessage: Message = {
                id: uuidv4(),
                content: message,
                sender: 'user',
                timestamp: new Date(),
            }

            const aiMessageId = uuidv4()
            const aiMessage: Message = {
                id: aiMessageId,
                content: '',
                sender: 'ai',
                timestamp: new Date(),
            }

            addMessage(userMessage)
            addMessage(aiMessage)
            setIsLoading(true)

            try {
                await chatService.sendMessage(message, sessionId, (content) => updateMessage(aiMessageId, content))
            } catch (error) {
                console.error('Error sending message:', error)
                updateMessage(aiMessageId, 'An error occurred while sending the message.')
            } finally {
                setIsLoading(false)
                updateProjectData()
            }
        },
        [isLoading, projectSession, addMessage, updateMessage, setProjectSession, setIsLoading, updateProjectData, chatService],
    )

    const generateTasks = useCallback(async () => {
        if (!projectSession) {
            const errorMessage: Message = {
                id: uuidv4(),
                content: 'Please start a project planning conversation first before generating tasks.',
                sender: 'ai',
                timestamp: new Date(),
            }
            addMessage(errorMessage)
            return
        }

        setIsLoading(true)
        setTasks([])
        const userMessage: Message = {
            id: uuidv4(),
            content: 'Generate tasks for this project',
            sender: 'user',
            timestamp: new Date(),
        }

        const taskGenerationMessage: Message = {
            id: uuidv4(),
            content: 'Generating detailed project tasks...',
            sender: 'ai',
            timestamp: new Date(),
        }

        addMessage(userMessage)
        addMessage(taskGenerationMessage)

        try {
            await chatService.generateTasks(
                projectSession,
                (task) => {
                    addTask(task)
                    if (!showTasksPanel) {
                        setShowTasksPanel(true)
                    }
                },
                (count) => {
                    updateMessage(
                        taskGenerationMessage.id,
                        `I've generated ${count} tasks for your project! You can view them in the panel on the right.`,
                    )
                },
            )
        } catch (error) {
            console.error('Error generating tasks:', error)
            updateMessage(taskGenerationMessage.id, 'An error occurred while generating tasks.')
        } finally {
            setIsLoading(false)
        }
    }, [projectSession, showTasksPanel, addMessage, addTask, updateMessage, setShowTasksPanel, setIsLoading, chatService])

    const publishTasks = useCallback(async () => {
        const activeWorkspaceId = getActiveWorkspaceId()
        const userId = getUserId()
    
        // Validation
        if (!activeWorkspaceId) {
            toast.error('Please select a workspace before publishing tasks.')
            return
        }
    
        if (!user || !userId) {
            toast.error('Please log in to publish tasks.')
            return
        }
    
        if (tasks.length === 0) {
            toast.error('Please generate tasks first.')
            return
        }
    
        // Use selected project name if available, otherwise fall back to extracted name
        const projectName = selectedProject?.name || projectData.name || 'Untitled Project'
        // Convert project ID to string if it's a number, or keep as null if undefined
        const existingProjectId = selectedProject?.id ? String(selectedProject.id) : null
    
        setIsSaving(true)
    
        try {
            const result = await saveProject(
                userId,
                projectName,
                projectData.description,
                activeWorkspaceId,
                tasks,
                projectData.techStack,
                existingProjectId // Pass the existing project ID
            )
    
            if (result?.status === 'success') {
                const isNewProject = result.data?.project?.isNew ?? true
                const toastMessage = isNewProject 
                    ? `Project "${projectName}" has been successfully created.`
                    : `Tasks have been successfully added to project "${projectName}".`
                
                toast.success(toastMessage)
    
                // Reset state
                setShowTasksPanel(false)
                if (typeof setSelectedProject === 'function') {
                    setSelectedProject(null) // Reset selected project
                }
    
                // Add success message to chat
                const aiMessage: Message = {
                    id: uuidv4(),
                    content: isNewProject 
                        ? `Project "${projectName}" has been created successfully. You can view it in your projects dashboard.`
                        : `Tasks have been added to project "${projectName}" successfully. You can view them in your projects dashboard.`,
                    sender: 'ai',
                    timestamp: new Date(),
                }
                addMessage(aiMessage)
            } else {
                throw new Error(result?.message || 'Failed to save project')
            }
        } catch (error) {
            console.error('Error publishing tasks:', error)
            toast.error('Failed to save project. Please try again.')
        } finally {
            setIsSaving(false)
        }
    }, [
        tasks,
        projectData,
        selectedProject,
        getUserId,
        getActiveWorkspaceId,
        user,
        setIsSaving,
        setShowTasksPanel,
        addMessage,
        setSelectedProject,
    ])

    return {
        // State
        messages,
        tasks,
        projectData,
        showTasksPanel,
        isLoading,
        isSaving,
        hasMessages: messages.length > 0,
        selectedProject,

        // Actions
        sendMessage,
        generateTasks,
        publishTasks,
        resetProject,
        setSelectedProject,
    }
}
