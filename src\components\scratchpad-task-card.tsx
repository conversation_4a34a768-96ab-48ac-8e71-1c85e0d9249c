import { motion } from 'framer-motion'
import { ArrowUpRight, Link2, MoreH<PERSON>zon<PERSON> } from 'lucide-react'
import React from 'react'
import TimeEstimateChip from './ui/time-estimate-chip'
import ChecklistChip from './ui/checklist-chip'
import DepartmentChip from './ui/department-chip'

interface TaskCardProps {
    taskNum: number
    id: string
    category: string
    assignedRole: string
    description: string
    estimatedHours: number
    title: string
    dependencies: string[]
}

const TaskCard: React.FC<TaskCardProps> = ({ taskNum, id, category, description, estimatedHours, title, dependencies }) => {
    return (
        <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, type: 'spring', stiffness: 100, damping: 12 }}
            className="flex flex-row">
            <div className="flex flex-row ml-[10px] border-l border-zinc-300 relative">
                <div className="absolute -left-[10px] h-[20px] w-[20px] px-[8px] py-[4px] flex justify-center items-center rounded-[6px] bg-zinc-100 border border-[#38383812]">
                    <span className="font-bold text-[#60646C] text-center text-[13px]">{taskNum}</span>
                </div>
                {dependencies && dependencies.length > 0 && (
                    <div className="absolute -left-[10px] top-9 h-[20px] w-[20px] p-[2px] flex justify-center items-center rounded-[6px] bg-slate-200 border border-[#38383812]">
                        <Link2 size={17} className="text-zinc-600" />
                    </div>
                )}
            </div>
            <div className="pl-[10px] pt-0 p-[30px] mt-0 m-[13px] w-full border-b border-zinc-200">
                <div className="flex flex-row items-center justify-between">
                    <div className="flex items-center text-gray-500 text-[12px]">
                        <span className="font-semibold underline">{id}</span>
                        <ArrowUpRight size={14} className="ml-0.5" />
                    </div>
                    <MoreHorizontal size={20} className="cursor-pointer text-zinc-400" />
                </div>
                <div className="flex items-center space-x-2 mt-[13px] text-gray-600">
                    {/* Department chip */}
                    <DepartmentChip shortCode={category} size="sm" />
                    {/* Estimated hours chip */}
                    <TimeEstimateChip estimate={estimatedHours} size="sm" />
                    {/* Steps chip */}
                    <ChecklistChip steps={dependencies.length} size="sm" />
                </div>
                <h3 className="text-md font-semibold py-[6px]">{title}</h3>
                <p className="text-sm">{description}</p>
            </div>
        </motion.div>
    )
}

export default TaskCard
