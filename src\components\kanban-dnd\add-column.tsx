'use client'

import { useState } from 'react'
import { useKanbanStore } from '@/store/kanban-dnd-store'
import { PlusCircle, X } from 'lucide-react'

export default function AddColumn() {
    const { addColumn } = useKanbanStore()
    const [isAdding, setIsAdding] = useState(false)
    const [columnName, setColumnName] = useState('')
    const [columnColor, setColumnColor] = useState('#E0E0E0')

    function handleAddColumn() {
        if (!columnName.trim()) return

        const columns = useKanbanStore.getState().columns
        const newColumnId = Math.max(0, ...columns.map((c) => c.id)) + 1
        const shortCode = columnName.toLowerCase().replace(/\s+/g, '_')

        addColumn({
            id: newColumnId,
            short_code: shortCode,
            label: columnName,
            kanban_column: true,
            column_order: columns.length + 1,
            colour: columnColor,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            deletedAt: null,
        })

        setIsAdding(false)
        setColumnName('')
        setColumnColor('#E0E0E0')
    }

    if (isAdding) {
        return (
            <div className="w-[300px] bg-white rounded-md shadow border border-gray-200 p-3 flex-shrink-0">
                <div className="flex justify-between items-center mb-3">
                    <h3 className="font-medium">Add New Column</h3>
                    <button onClick={() => setIsAdding(false)} className="text-gray-400 hover:text-gray-600">
                        <X size={18} />
                    </button>
                </div>

                <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Column Name</label>
                    <input
                        type="text"
                        value={columnName}
                        onChange={(e) => setColumnName(e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., To Do"
                        autoFocus
                    />
                </div>

                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Column Color</label>
                    <div className="flex items-center gap-2">
                        <input
                            type="color"
                            value={columnColor}
                            onChange={(e) => setColumnColor(e.target.value)}
                            className="w-8 h-8 rounded-md cursor-pointer"
                        />
                        <span className="text-sm text-gray-500">{columnColor}</span>
                    </div>
                </div>

                <button
                    onClick={handleAddColumn}
                    disabled={!columnName.trim()}
                    className="w-full py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors">
                    Add Column
                </button>
            </div>
        )
    }

    return (
        <button
            onClick={() => setIsAdding(true)}
            className="w-[300px] h-[400px] border-2 border-dashed border-gray-300 rounded-md flex flex-col items-center justify-center gap-2 text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors flex-shrink-0">
            <PlusCircle size={24} />
            <span className="font-medium">Add New Column</span>
        </button>
    )
}
