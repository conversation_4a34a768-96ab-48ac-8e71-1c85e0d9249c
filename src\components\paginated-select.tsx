'use client'

import * as React from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import { Loader2 } from 'lucide-react'
import debounce from 'lodash/debounce'
import { Search } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'

import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'

// Optimized utility function for better performance
const getNestedValue = (obj: unknown, path: string) => {
    return path.split('.').reduce((prev, curr) => (prev ? (prev as Record<string, unknown>)[curr] : null), obj)
}

export type InfiniteSelectProps<T> = {
    endpoint: string
    onSelectionChange: (selectedItems: T[]) => void
    multiple?: boolean
    returnKeys?: string[]
    placeholder?: string
    disabled?: boolean
    className?: string
    triggerClassName?: string
    contentClassName?: string
    itemClassName?: string
    pageSize?: number
    labelKey?: string
    valueKey?: string
    initialSelected?: T[]
    params?: Record<string, unknown>
    icon?: React.ReactNode
    dataAccessor?: string
    enableSearch?: boolean
    searchPlaceholder?: string
    searchParam?: string
    labelModifier?: (label: string, item: T) => string | React.ReactNode
    showNoMoreItems?: boolean
    defaultValueLabel?: string
    resetTrigger?: boolean
}

export function PaginatedSelect<T extends Record<string, unknown>>({
    endpoint,
    onSelectionChange,
    multiple = false,
    returnKeys,
    placeholder = 'Select an item',
    disabled = false,
    className,
    triggerClassName,
    contentClassName,
    itemClassName,
    pageSize = 10,
    labelKey = 'name',
    valueKey = 'id',
    initialSelected = [],
    params,
    icon,
    dataAccessor = 'projectData',
    enableSearch = true,
    searchParam = 'searchQuery',
    searchPlaceholder = 'Search...',
    labelModifier,
    showNoMoreItems = false,
    defaultValueLabel,
    resetTrigger
}: InfiniteSelectProps<T>) {
    const [open, setOpen] = React.useState(false)
    const [selectedItems, setSelectedItems] = React.useState<T[]>(initialSelected)
    const contentRef = React.useRef<HTMLDivElement>(null)
    const ITEM_HEIGHT_MAPPING = {
        1: 'h-8',
        2: 'h-16',
        3: 'h-24',
    }

    // React 19: Store latest callback without causing re-renders
    const onSelectionChangeRef = React.useRef(onSelectionChange)
    onSelectionChangeRef.current = onSelectionChange

    const [searchQuery, setSearchQuery] = React.useState('')
    const [debouncedSearchQuery, setDebouncedSearchQuery] = React.useState('')

    // Create debounced search function with better cleanup
    const debouncedSearch = React.useMemo(
        () =>
            debounce((value: string) => {
                setDebouncedSearchQuery(value)
            }, 300),
        [],
    )

    // Cleanup debounced function on unmount
    React.useEffect(() => {
        return () => {
            debouncedSearch.cancel()
        }
    }, [debouncedSearch])

    // Update query params to include search
    const queryParams = React.useMemo(
        () => ({
            ...params,
            ...(debouncedSearchQuery ? { [searchParam]: debouncedSearchQuery } : {}),
        }),
        [params, debouncedSearchQuery, searchParam],
    )

    // Fetch data using React Query with performance optimizations
    const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, isError, error } = useInfiniteQuery({
        queryKey: ['infinite-select', endpoint, queryParams, resetTrigger],
        initialPageParam: 1,
        queryFn: async ({ pageParam = 1 }) => {
            try {
                const response = await api.get(endpoint, {
                    params: {
                        ...queryParams,
                        page: pageParam,
                        limit: pageSize,
                    },
                })
                return response.data.data
            } catch (error) {
                toast.error('Error fetching projects. Please try again later.')
                throw error
            }
        },
        getNextPageParam: (lastPage) => {
            const { paginationData } = lastPage
            return paginationData?.currentPage < paginationData?.totalPages ? paginationData?.currentPage + 1 : undefined
        },
        enabled: open || defaultValueLabel !== undefined,
        // Performance optimizations
        staleTime: 1 * 60 * 1000, // 5 minutes
        gcTime: 5 * 60 * 1000, // 10 minutes (formerly cacheTime)
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        retry: 2,
    })
    React.useEffect(() => {
        if (resetTrigger && !defaultValueLabel) {
            setSelectedItems([])
        }
    }, [resetTrigger])

    // Optimized scroll handler with throttling
    const handleScroll = React.useCallback(
        (e: React.UIEvent<HTMLDivElement>) => {
            const { scrollTop, scrollHeight, clientHeight } = e.currentTarget

            if (scrollHeight - scrollTop <= clientHeight * 1.5) {
                if (hasNextPage && !isFetchingNextPage) {
                    fetchNextPage()
                }
            }
        },
        [hasNextPage, isFetchingNextPage, fetchNextPage],
    )

    // Optimized processing of all items from all pages
    const allItems = React.useMemo(() => {
        if (!data?.pages?.length) return []

        const firstPage = data.pages[0]
        const isContainArray = Array.isArray(firstPage)

        const allOptions = isContainArray
            ? data.pages.flatMap((page) => page) || []
            : data.pages.flatMap((page) => page?.[dataAccessor]) || []

        return allOptions.filter(Boolean) // Remove any null/undefined items
    }, [data?.pages, dataAccessor])

    React.useEffect(() => {
        if (defaultValueLabel && allItems.length > 0) {
            const defaultValue = allItems.find((item) => {
                const label = String(getNestedValue(item, labelKey as string)).toLowerCase()
                return label === defaultValueLabel.toLowerCase()
            })

            if (defaultValue) {
                setSelectedItems([defaultValue])
                onSelectionChangeRef.current([defaultValue])
            }
        }
    }, [defaultValueLabel, allItems, labelKey])

    // Memoized selected items map for faster lookups
    const selectedItemsMap = React.useMemo(() => {
        const map = new Set<string>()
        selectedItems.forEach((item) => {
            map.add(String(getNestedValue(item, valueKey as string)))
        })
        return map
    }, [selectedItems, valueKey])

    // Optimized transform function for returnKeys
    const transformItemsForReturn = React.useCallback(
        (items: T[]) => {
            if (!returnKeys?.length) return items

            return items.map((item) => {
                const result: Record<string, unknown> = {}
                returnKeys.forEach((key) => {
                    const keyStr = key as string
                    result[keyStr] = getNestedValue(item, keyStr)
                })
                return result as T
            })
        },
        [returnKeys],
    )

    // Optimized selection change handler
    const handleValueChange = React.useCallback(
        (value: string) => {
            if (!allItems.length) return

            // Find the selected item
            const selectedItem = allItems.find((item) => String(getNestedValue(item, valueKey as string)) === value)
            if (!selectedItem) return

            if (multiple) {
                // For multiple selection, toggle the item
                const isAlreadySelected = selectedItemsMap.has(value)

                const newSelectedItems = isAlreadySelected
                    ? selectedItems.filter((item) => String(getNestedValue(item, valueKey as string)) !== value)
                    : [...selectedItems, selectedItem]

                setSelectedItems(newSelectedItems)
                onSelectionChangeRef.current(transformItemsForReturn(newSelectedItems))
            } else {
                // For single selection, replace the selection
                const newSelection = [selectedItem]
                setSelectedItems(newSelection)
                setOpen(false)
                onSelectionChangeRef.current(transformItemsForReturn(newSelection))
            }

            // Clear search queries on selection
            setSearchQuery('')
            setDebouncedSearchQuery('')
        },
        [allItems, valueKey, multiple, selectedItems, selectedItemsMap, transformItemsForReturn],
    )

    // Optimized display value computation
    const displayValue = React.useMemo(() => {
        if (!selectedItems.length) return ''

        if (multiple) {
            return selectedItems.map((item) => String(getNestedValue(item, labelKey as string))).join(', ')
        } else {
            return String(getNestedValue(selectedItems[0], labelKey as string))
        }
    }, [selectedItems, labelKey, multiple])

    // Optimized open change handler
    const onOpenChange = React.useCallback(
        (open: boolean) => {
            setOpen(open)
            if (!open) {
                // Clear search queries when closing
                setSearchQuery('')
                setDebouncedSearchQuery('')
                debouncedSearch.cancel() // Cancel any pending debounced calls
            }
        },
        [debouncedSearch],
    )

    const handleSearchInputChange = React.useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value
            setSearchQuery(value)
            debouncedSearch(value)
        },
        [debouncedSearch],
    )

    return (
        <div className={cn('relative', className)}>
            <Select
                open={open}
                onOpenChange={onOpenChange}
                value={selectedItems.length ? String(getNestedValue(selectedItems[0], valueKey as string)) : ''}
                disabled={disabled || isLoading}
                onValueChange={handleValueChange}>
                <SelectTrigger className={cn('w-full', isLoading && 'opacity-70 cursor-not-allowed', triggerClassName)}>
                    {isLoading && defaultValueLabel ? (
                        <div className="p-2 text-center">
                            <Loader2 className="h-4 w-4 animate-spin mx-auto" />
                        </div>
                    ) : (
                        <>
                            {icon && icon}
                            {multiple && selectedItems.length > 0 ? (
                                <div className="flex flex-wrap gap-1 max-w-[calc(100%-20px)]">
                                    {selectedItems.slice(0, 2).map((item, index) => {
                                        const label = getNestedValue(item, labelKey as string)

                                        return (
                                            <Badge key={index} variant="secondary" className="truncate max-w-[150px]">
                                                {String(label)}
                                            </Badge>
                                        )
                                    })}
                                    {selectedItems.length > 2 && <Badge variant="secondary">+{selectedItems.length - 2}</Badge>}
                                </div>
                            ) : (
                                <SelectValue placeholder={placeholder}>{displayValue}</SelectValue>
                            )}
                        </>
                    )}
                </SelectTrigger>
                <SelectContent
                    ref={contentRef}
                    className={cn('min-w-[var(--radix-select-trigger-width)]', contentClassName)}
                    position="popper">
                    {/* Add search input */}
                    {enableSearch && (
                        <div className="sticky top-0 p-2 bg-transparent border-b z-10 mb-1">
                            <div className="relative">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    type="text"
                                    placeholder={searchPlaceholder}
                                    value={searchQuery}
                                    onChange={handleSearchInputChange}
                                    className="pl-8 h-8 border-none shadow-none focus:ring-0 focus-visible:ring-0 focus-visible:outline-none focus:border-slate-200"
                                />
                            </div>
                        </div>
                    )}

                    <ScrollArea
                        className={cn(
                            'h-30',
                            allItems?.length < 4 && ITEM_HEIGHT_MAPPING[allItems.length as keyof typeof ITEM_HEIGHT_MAPPING],
                        )}
                        onScroll={handleScroll}>
                        <SelectGroup>
                            {isError ? (
                                <div className="p-2 text-center text-destructive">Error: {(error as Error).message}</div>
                            ) : allItems.length > 0 ? (
                                allItems.map((item) => {
                                    const value = String(getNestedValue(item, valueKey as string))
                                    const label = String(getNestedValue(item, labelKey as string))
                                    const isSelected = selectedItemsMap.has(value)

                                    return (
                                        <SelectItem
                                            key={value}
                                            value={value}
                                            className={cn(
                                                'flex items-center justify-between',
                                                isSelected && 'font-medium',
                                                itemClassName,
                                            )}>
                                            {labelModifier ? labelModifier(label, item) : <span>{label}</span>}
                                        </SelectItem>
                                    )
                                })
                            ) : !isLoading ? (
                                <div className="p-2 text-center text-muted-foreground">No items found</div>
                            ) : null}

                            {(isLoading || isFetchingNextPage) && (
                                <div className="p-2 text-center">
                                    <Loader2 className="h-4 w-4 animate-spin mx-auto" />
                                </div>
                            )}

                            {!hasNextPage && allItems.length > 0 && !isLoading && showNoMoreItems && (
                                <div className="p-2 text-center text-muted-foreground text-xs">No more items</div>
                            )}
                        </SelectGroup>
                    </ScrollArea>
                </SelectContent>
            </Select>
        </div>
    )
}
