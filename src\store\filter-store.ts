import { create } from 'zustand'

type FilterState = {
    // Active filter category
    activeCategory: string | null
    activeEndPoint: string | null
    // Search query for filtering options
    searchQuery: string

    // Selected filters by category (applied filters)
    selectedFilters: Record<string, number[]>

    // Temporary selected filters (before applying)
    tempSelectedFilters: Record<string, number[]>

    // Dialog open state
    isDialogOpen: boolean

    // Actions
    setSelectedFilters: (filters: Record<string, number[]>) => void
    setTempSelectedFilters: (filters: Record<string, number[]>) => void
    setActiveEndpoint: (endpoint: string) => void
    setDialogOpen: (open: boolean) => void
    setActiveCategory: (category: string) => void
    setSearchQuery: (query: string) => void
    initTempFilters: () => void
    toggleTempFilter: (category: string, filterId: number, isRadio?: boolean) => void
    applyFilters: () => void
    cancelFilters: () => void
    clearCategoryFilters: (category: string) => void
    clearAllFilters: () => void
    getSelectedCountByCategory: (category: string) => number
    getTempSelectedCountByCategory: (category: string) => number
    getTotalSelectedCount: () => number
    getTotalTempSelectedCount: () => number
}

export const useFilterStore = create<FilterState>()((set, get) => ({
    // Initial state
    activeCategory: null,
    searchQuery: '',
    selectedFilters: {},
    tempSelectedFilters: {},
    isDialogOpen: false,
    activeEndPoint: '',

    // Set dialog open state
    setSelectedFilters: (filters) => {
        set({ selectedFilters: filters })
    },
    setTempSelectedFilters: (filters) => {
        set({ tempSelectedFilters: filters })
    },
    setDialogOpen: (open) => {
        set({ isDialogOpen: open })

        // Initialize temp filters when dialog opens
        if (open) {
            get().initTempFilters()
        }
    },
    setActiveEndpoint: (endpoint) => {
        set({ activeEndPoint: endpoint })
    },

    // Set active category
    setActiveCategory: (category) => {
        set({ activeCategory: category })
    },

    // Set search query
    setSearchQuery: (query) => {
        set({ searchQuery: query })
    },

    // Initialize temporary filters with current selection
    initTempFilters: () => {
        const { selectedFilters } = get()

        // Create a deep copy of selectedFilters
        const tempFilters: Record<string, number[]> = {}
        Object.keys(selectedFilters).forEach((category) => {
            tempFilters[category] = [...(selectedFilters[category] || [])]
        })

        set({ tempSelectedFilters: tempFilters })
    },

    // Toggle a filter in temporary selection
    toggleTempFilter: (category, filterId, isRadio = false) => {
        const currentTempFilters = { ...get().tempSelectedFilters }

        // Initialize the category array if it doesn't exist
        if (!currentTempFilters[category]) {
            currentTempFilters[category] = []
        }

        if (isRadio) {
            // Radio mode: set only the selected filter
            const categoryFilters = [...currentTempFilters[category]]
            const index = categoryFilters.indexOf(filterId)
            if (index > -1) {
                currentTempFilters[category] = [] // remove if exists
            } else {
                currentTempFilters[category] = [filterId]
            }
        } else {
            // Multi-select mode
            const categoryFilters = [...currentTempFilters[category]]
            const index = categoryFilters.indexOf(filterId)

            if (index > -1) {
                categoryFilters.splice(index, 1) // remove if exists
            } else {
                categoryFilters.push(filterId) // add if not exists
            }

            currentTempFilters[category] = categoryFilters
        }

        set({ tempSelectedFilters: currentTempFilters })
    },

    // Apply all temporary filters to selected filters
    applyFilters: () => {
        const { tempSelectedFilters } = get()
        set({
            selectedFilters: JSON.parse(JSON.stringify(tempSelectedFilters)),
            isDialogOpen: false,
        })
    },

    // Cancel changes and revert to previous selection
    cancelFilters: () => {
        set({ isDialogOpen: false })
    },

    // Clear all filters for a category
    clearCategoryFilters: (category) => {
        const currentTempFilters = { ...get().tempSelectedFilters }
        currentTempFilters[category] = []

        set({ tempSelectedFilters: currentTempFilters })
    },

    // Clear all filters
    clearAllFilters: () => {
        set({
            tempSelectedFilters: {},
        })
    },

    // Get count of selected filters for a category
    getSelectedCountByCategory: (category) => {
        return get().selectedFilters[category]?.length || 0
    },

    // Get count of temporary selected filters for a category
    getTempSelectedCountByCategory: (category) => {
        return get().tempSelectedFilters[category]?.length || 0
    },

    // Get total count of selected filters
    getTotalSelectedCount: () => {
        const { selectedFilters } = get()
        return Object.values(selectedFilters).reduce((total, filters) => total + filters.length, 0)
    },

    // Get total count of temporary selected filters
    getTotalTempSelectedCount: () => {
        const { tempSelectedFilters } = get()
        return Object.values(tempSelectedFilters).reduce((total, filters) => total + filters.length, 0)
    },
}))
