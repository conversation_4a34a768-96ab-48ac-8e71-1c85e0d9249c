'use client'
import { X } from 'lucide-react'
import Image from 'next/image'

interface ScratchpadTutorialProps {
    onClick: () => void
}

interface TutorialCardProps {
    icon: string
    bgColor: string
    iconBgColor: string
    title: string
    description: string
}

export default function ScratchpadTutorial({ onClick }: ScratchpadTutorialProps) {
    return (
        <div className="border border-[#e8e8edba] rounded-[10px] py-[15px] px-[20px] max-w-9/11">
            <div className="flex flex-row justify-between items-center">
                <h1 className="font-semibold">Learn how to use Scratchpad ...</h1>
                <button
                    onClick={onClick}
                    className="hover:bg-gray-100 p-1 rounded-full transition-colors"
                    aria-label="Close tutorial">
                    <X size={24} className="cursor-pointer" />
                </button>
            </div>
            <div className="flex flex-row py-[16px] gap-16 flex-wrap md:flex-nowrap">
                <TutorialCard
                    icon="/assets/icons/tabler_run.png"
                    bgColor="bg-[#F5EFFF]"
                    iconBgColor="bg-[#E5D9F2]"
                    title="Want to keep going?"
                    description="Choose a project from the list below and pick up where you left off."
                />
                <TutorialCard
                    icon="/assets/icons/new-born.png"
                    bgColor="bg-[#FFF2D7]"
                    iconBgColor="bg-[#FFE0B5]"
                    title="Starting something new?"
                    description='Click "Create New Project" to begin fresh'
                />
                <TutorialCard
                    icon="/assets/icons/open-arm-fill.png"
                    bgColor="bg-[#F9F6E6]"
                    iconBgColor="bg-[#E1EACD]"
                    title="Come Back Anytime"
                    description="Your project is always here waiting for you—pick up right where you left off."
                />
            </div>
        </div>
    )
}

function TutorialCard({ icon, bgColor, iconBgColor, title, description }: TutorialCardProps) {
    return (
        <div className="flex flex-row rounded-[16px] border border-[#e8e8ed] bg-white p-[10px] justify-between flex-1 max-w-[350px] hover:shadow-md transition-shadow duration-200 hover:border-gray-300 cursor-pointer">
            <div className={`rounded-[5px] ${bgColor} py-[14px] px-[6px] w-[42px] flex justify-center items-center`}>
                <div className={`rounded-[111px] ${iconBgColor} p-[6px]`}>
                    <Image src={icon} width={25} height={25} alt="Icon" />
                </div>
            </div>
            <div className="pl-[10px] flex flex-col flex-1">
                <p className="text-[14px] font-bold text-[#171A1F]">{title}</p>
                <p className="text-[#9095A1] text-[12px]">{description}</p>
            </div>
        </div>
    )
}
