import React from 'react'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface TooltipCardProps {
    trigger: React.ReactNode
    children: React.ReactNode
}

export function TooltipCard({ trigger, children }: TooltipCardProps) {
    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild className="cursor-pointer">
                    {trigger}
                </TooltipTrigger>
                <TooltipContent className="w-[300px] p-0 bg-transparent" hideArrow>
                    {children}
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    )
}
