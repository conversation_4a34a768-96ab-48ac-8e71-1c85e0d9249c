'use client'

import { useMutation } from '@tanstack/react-query'
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { api } from '@/config/axios-config'

interface DeleteWithAlertProps {
    title?: string
    description?: string
    triggerText?: string
    endpoint: string
    method?: 'DELETE' | 'POST' | 'PUT'
    onAfterSuccess?: (response: unknown) => void
    onError?: (error: unknown) => void
    cancelText?: string
    confirmText?: string
    children?: React.ReactNode
    isAlertOpen?: boolean
    setIsAlertOpen?: (state: boolean) => void
}

export function DeleteWithAlert({
    title = 'Are you sure?',
    description = 'This action cannot be undone.',
    endpoint,
    onAfterSuccess,
    cancelText = 'Cancel',
    confirmText = 'Delete',
    children,
    isAlertOpen,
    setIsAlertOpen,
}: DeleteWithAlertProps) {
    const mutation = useMutation({
        mutationFn: async () => {
            const response = await api.delete(endpoint)
            return response.data
        },
        onSuccess: (data) => {
            if (onAfterSuccess) onAfterSuccess(data)
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to create feature. Please try again.')
        },
    })

    return (
        <AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
            <AlertDialogTrigger className="cursor-pointer">{children}</AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>{title}</AlertDialogTitle>
                    <AlertDialogDescription>{description}</AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel disabled={mutation.isPending}>{cancelText}</AlertDialogCancel>
                    <AlertDialogAction onClick={() => mutation.mutate()} disabled={mutation.isPending}>
                        {mutation.isPending ? 'Processing...' : confirmText}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}
