'use client'

import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { useMutation } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { useAuthStore } from '@/store/auth.store'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useState } from 'react'
import { Switch } from '@/components/ui/switch'

const formSchema = z.object({
    title: z.string().min(1, 'Feature title is required'),
    description: z.string().min(10, 'Description must be at least 10 characters'),
    draft: z.boolean(),
})

type FormValues = z.infer<typeof formSchema>

interface CreateFeatureModalProps {
    children: React.ReactNode
    onSuccess?: () => void
    projectId: number
}

export default function CreateFeatureModal({ children, onSuccess, projectId }: CreateFeatureModalProps) {
    const currentWorkspace = useAuthStore((state) => state.currentWorkspace)
    const [isOpen, setIsOpen] = useState<boolean>(false)

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            title: '',
            description: '',
            draft: false,
        },
    })

    const mutation = useMutation({
        mutationFn: async (data: FormValues) => {
            const payload = {
                feat_code: `FEAT-${Math.floor(Math.random() * 1000)}`,
                title: data.title,
                description: data.description,
                workspace_id: currentWorkspace?.id,
                status_id: 1,
                draft: data.draft,
                project_id: projectId,
            }
            const response = await api.post(endpoints.features.createFeature, payload)
            return response.data
        },
        onSuccess: () => {
            toast.success('Feature created successfully!')
            form.reset()
            if (onSuccess) onSuccess()
            setIsOpen(false)
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to create feature. Please try again.')
        },
    })

    const onSubmit = (data: FormValues) => {
        const toastId = toast.loading('Creating feature...')
        mutation.mutate(data, {
            onSettled: () => {
                toast.dismiss(toastId)
            },
        })
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>{children}</DialogTrigger>
            <DialogContent className="sm:max-w-[556px]">
                <DialogHeader>
                    <div className="flex items-start mb-2">
                        <div>
                            <DialogTitle className="text-lg font-semibold">Create Feature</DialogTitle>
                        </div>
                    </div>
                </DialogHeader>

                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                            control={form.control}
                            name="title"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-sm text-[#414651] font-semibold">Feature Title*</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="Enter feature title"
                                            className="w-full focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none focus:border-slate-200"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="description"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-sm text-[#414651] font-semibold">Description*</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Describe your Feature in detail"
                                            className="min-h-[120px] w-full focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none focus:border-slate-200"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="draft"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-sm text-[#414651] font-semibold">Draft*</FormLabel>
                                    <FormControl>
                                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <Button
                            type="submit"
                            className="w-full bg-gray-800 hover:bg-gray-700 text-white py-6 font-semibold text-md max-h-[43px]"
                            disabled={mutation.isPending}>
                            {mutation.isPending ? 'Creating...' : 'Create Feature'}
                        </Button>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    )
}
