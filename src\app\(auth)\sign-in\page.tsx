'use client'

import dynamic from 'next/dynamic'
import { Suspense } from 'react'
import { Loader } from 'lucide-react'

const SignInContent = dynamic(() => import('./sign-in-form'), {
    ssr: false,
})

export default function Page() {
    return (
        <Suspense
            fallback={
                <div className="flex justify-center items-center min-h-screen">
                    <Loader className="w-6 h-6 animate-spin" />
                </div>
            }>
            <SignInContent />
        </Suspense>
    )
}
