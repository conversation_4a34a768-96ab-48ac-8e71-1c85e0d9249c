'use client'

import { useState, useEffect } from 'react'
import { FilterSidebar } from './filter-sidebar'
import { FilterPanel } from './filter-panel'
import { useFilterStore } from '@/store/filter-store'
import { Button } from '@/components/ui/button'
import type { FilterCategory } from './types'

interface FilterContainerProps {
    categories: FilterCategory[]
    defaultApiEndpoint?: string
    params?: Record<string, unknown>
}

export function FilterContainer({ categories, defaultApiEndpoint = '/api/filters' }: FilterContainerProps) {
    const {
        activeCategory,
        getTotalSelectedCount,
        applyFilters,
        cancelFilters,
        // clearAllFilters,
        getTotalTempSelectedCount,
    } = useFilterStore()

    const [selectedCategory, setSelectedCategory] = useState<FilterCategory | null>(null)

    // Update selected category when activeCategory changes
    useEffect(() => {
        if (activeCategory) {
            const category = categories.find((c) => c.id === activeCategory)
            setSelectedCategory(category || null)
        } else {
            setSelectedCategory(null)
        }
    }, [activeCategory, categories])

    const totalSelectedCount = getTotalSelectedCount()
    const tempSelectedCount = getTotalTempSelectedCount()
    const isButtonDisabled = totalSelectedCount === 0 && tempSelectedCount === 0

    return (
        <>
            <div className="flex flex-1 overflow-hidden h-full">
                <div className="w-64 overflow-auto border-r">
                    <FilterSidebar categories={categories} />
                </div>
                <div className="w-full ">
                    <div className="flex-1 overflow-hidden">
                        {selectedCategory ? (
                            <FilterPanel
                                category={selectedCategory}
                                endpoint={selectedCategory.endpoint || defaultApiEndpoint}
                                params={selectedCategory?.params}
                            />
                        ) : (
                            <div className="flex h-full items-center justify-center">
                                <p className="text-muted-foreground">Select a category to view filters</p>
                            </div>
                        )}
                    </div>
                    <div className="flex items-center justify-end px-4">
                        <div className="flex gap-2 ">
                            <Button variant="ghost" onClick={cancelFilters}>
                                Cancel
                            </Button>
                            <Button disabled={isButtonDisabled} onClick={applyFilters}>
                                Apply
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}
