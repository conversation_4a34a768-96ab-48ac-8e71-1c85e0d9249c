'use client'

import { useEffect } from 'react'

import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { ProjectDetails } from './project-details'
import ProjectBottomTabs from './bottom-tab-section'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useQuery } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { useParams } from 'next/navigation'
import endpoints from '@/services/api-endpoints'
import { Loader } from 'lucide-react'
import { Separator } from '@/components/ui/separator'

export default function Page() {
    const { setBreadcrumbs } = useBreadcrumbStore()

    const params = useParams()
    const projectId = params.id

    // Fetch project data
    const { data: project, isLoading } = useQuery({
        queryKey: ['project', projectId],
        queryFn: async () => {
            try {
                const response = await api.get(`${endpoints.project.listProjects}/${projectId}`)
                return response.data.data
            } catch (error) {
                toast.error('Failed to fetch project details')
                throw error
            }
        },
        enabled: !!projectId,
        staleTime: 1 * 60 * 1000, // 1 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
    })

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/app' },
            { label: 'Projects', href: '/app/projects' },
            { label: project?.name || 'Project Name', href: `/app/projects/${projectId}` },
        ])
    }, [setBreadcrumbs, project?.name, projectId])

    if (isLoading) {
        return (
            <div className="w-full flex items-center min-h-screen justify-center shadow-none border-none bg-transparent pt-0">
                <Loader className="animate-spin" />
            </div>
        )
    }

    return (
        <div className="w-full space-y-4">
            <ScrollArea className="h-[90dvh] w-full bg-[#F9FAFB]" type="scroll">
                <div className="flex flex-col gap-4">
                    <ProjectDetails project={project} />
                    <Separator />
                    <ProjectBottomTabs />
                </div>
            </ScrollArea>
        </div>
    )
}
