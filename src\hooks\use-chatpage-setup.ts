import { useEffect } from 'react'
import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { useAuthStore } from '@/store/auth.store'

export const useChatPageSetup = () => {
    const { setBreadcrumbs } = useBreadcrumbStore()
    const { fetchMyDetails, user } = useAuthStore()

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/app' },
            { label: 'Chat', href: '/app' },
        ])
    }, [setBreadcrumbs])

    useEffect(() => {
        const fetchData = async () => {
            try {
                await fetchMyDetails()
            } catch (error) {
                console.error('Error fetching data:', error)
            }
        }
        fetchData()
    }, [fetchMyDetails])

    return {
        userName: user?.first_name || '',
    }
}
