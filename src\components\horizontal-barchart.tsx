'use client'

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YAxi<PERSON> } from 'recharts'
import { Card, CardContent } from '@/components/ui/card'
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart'

interface HorizontalBarChartProps {
    title?: string
    chartData: { status: string; count: number; fill?: string }[]
    dataKey: string
    labelKey: string
    barColor?: string
    showTooltip?: boolean
    height?: number
}

export function HorizontalBarChart({
    title = 'Bar Chart',
    chartData,
    dataKey,
    labelKey,
    showTooltip = true,
}: HorizontalBarChartProps) {
    const chartConfig: ChartConfig = {
        [dataKey]: {
            label: title,
            color: 'hsl(var(--chart-1))',
        },
    }

    return (
        <Card className="bg-transparent shadow-none border-0 p-0">
            <CardContent className="p-1">
                <ChartContainer config={chartConfig}>
                    <BarChart layout="vertical" data={chartData}>
                        <XAxis type="number" dataKey={dataK<PERSON>} hide />
                        <YAxis type="category" dataKey={labelKey} tickLine={false} tickMargin={0} axisLine={false} />
                        {showTooltip && <ChartTooltip cursor={false} content={<ChartTooltipContent />} />}
                        <Bar dataKey={dataKey} radius={5} />
                    </BarChart>
                </ChartContainer>
            </CardContent>
        </Card>
    )
}
