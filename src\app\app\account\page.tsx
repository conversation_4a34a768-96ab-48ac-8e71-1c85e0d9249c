'use client'
import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useAuthStore } from '@/store/auth.store'

export default function Page() {
    const [loading, setLoading] = useState<boolean>(true)
    const [error, setError] = useState<string | null>(null)
    const fetchMyDetails = useAuthStore((state) => state.fetchMyDetails)
    const user = useAuthStore((state) => state.user)

    useEffect(() => {
        async function fetchData() {
            try {
                setLoading(true)
                await fetchMyDetails()
            } catch (error) {
                console.error('Error fetching data:', error)
                setError('Failed to fetch user data')
            } finally {
                setLoading(false)
            }
        }

        fetchData()
    }, [fetchMyDetails])

    return (
        <motion.div
            className="p-8 max-w-4xl mx-auto max-h-[90dvh] overflow-auto custom-scroll"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}>
            <motion.h1
                className="text-3xl font-bold mb-6 text-gray-800"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.5 }}>
                Account
            </motion.h1>

            {loading && (
                <motion.div
                    className="flex justify-center py-8"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}>
                    <div className="animate-pulse flex space-x-4">
                        <div className="rounded-full bg-gray-200 h-12 w-12"></div>
                        <div className="flex-1 space-y-4 py-1">
                            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            <div className="space-y-2">
                                <div className="h-4 bg-gray-200 rounded"></div>
                                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                            </div>
                        </div>
                    </div>
                </motion.div>
            )}

            {error && (
                <motion.p
                    className="text-red-500 p-4 bg-red-50 rounded-lg border border-red-200"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}>
                    {error}
                </motion.p>
            )}

            {user && (
                <motion.div
                    className="bg-white p-6 rounded-lg shadow-md border border-gray-100"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}>
                    <motion.h2
                        className="text-xl font-semibold mb-4 text-gray-700"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.5, duration: 0.5 }}>
                        User Information
                    </motion.h2>
                    <motion.pre
                        className="bg-gray-50 p-4 rounded overflow-auto max-w-full text-sm"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.7, duration: 0.5 }}>
                        {JSON.stringify(user, null, 2)}
                    </motion.pre>
                </motion.div>
            )}
        </motion.div>
    )
}
