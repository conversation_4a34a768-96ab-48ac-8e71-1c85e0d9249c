import { useState, useCallback } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { Message } from '@/types/chat'

export const useChat = () => {
    const [messages, setMessages] = useState<Message[]>([])
    const [isLoading, setIsLoading] = useState(false)

    const resetChat = useCallback(() => {
        setMessages([])
        setIsLoading(false)
    }, [])

    const addMessage = useCallback((message: Message) => {
        setMessages((prev) => [...prev, message])
    }, [])

    const updateMessage = useCallback((id: string, content: string) => {
        setMessages((prev) => prev.map((msg) => (msg.id === id ? { ...msg, content } : msg)))
    }, [])

    const sendMessage = useCallback(
        async (inputValue: string) => {
            if (!inputValue.trim()) return

            const userMessage: Message = {
                id: uuidv4(),
                content: inputValue,
                sender: 'user',
                timestamp: new Date(),
            }

            addMessage(userMessage)
            setIsLoading(true)

            try {
                const aiMessageId = uuidv4()
                const aiMessage: Message = {
                    id: aiMessageId,
                    content: '',
                    sender: 'ai',
                    timestamp: new Date(),
                }

                addMessage(aiMessage)

                const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        Accept: 'text/event-stream',
                    },
                    body: JSON.stringify({ query: inputValue }),
                })

                if (!response.body) {
                    throw new Error('No response body from the server')
                }

                // Process stream inline to maintain the exact same logic
                const reader = response.body.getReader()
                const decoder = new TextDecoder('utf-8')
                let buffer = ''

                try {
                    while (true) {
                        const { done, value } = await reader.read()
                        if (done) break

                        const decodedChunk = decoder.decode(value)
                        buffer += decodedChunk
                        updateMessage(aiMessageId, buffer)
                    }
                } catch (streamError) {
                    console.error('Error reading event stream:', streamError)
                    updateMessage(aiMessageId, 'An error occurred while processing the data.')
                } finally {
                    reader.releaseLock()
                }
            } catch (error) {
                console.error('Error sending message:', error)
                addMessage({
                    id: uuidv4(),
                    content: 'An error occurred while sending the message.',
                    sender: 'ai',
                    timestamp: new Date(),
                })
            } finally {
                setIsLoading(false)
            }
        },
        [addMessage, updateMessage],
    )

    return {
        messages,
        isLoading,
        sendMessage,
        hasMessages: messages.length > 0,
        resetChat,
    }
}
